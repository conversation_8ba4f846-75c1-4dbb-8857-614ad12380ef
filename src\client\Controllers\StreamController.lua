-- StreamController.lua
-- Client-side streaming interface and controls

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local SoundService = game:GetService("SoundService")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

local StreamController = {}
local player = Players.LocalPlayer

-- Stream state
local isStreaming = false
local currentStreamData = {}
local streamStartTime = 0
local streamUI = nil
local dummyAnimations = {}

-- UI Elements (will be created dynamically)
local streamButton = nil
local statsDisplay = nil
local streamingIndicator = nil

function StreamController.initialize()
    print("StreamController: Initializing...")
    
    -- Connect to server events
    EventManager.Streaming.StartStream.OnClientEvent:Connect(StreamController.onStreamStartResponse)
    EventManager.Streaming.StopStream.OnClientEvent:Connect(StreamController.onStreamStopResponse)
    EventManager.Streaming.StreamEarningsUpdate.OnClientEvent:Connect(StreamController.onEarningsUpdate)
    
    -- Create UI
    StreamController.createStreamUI()
    
    -- Setup dummy animations
    StreamController.setupDummyAnimations()
    
    print("StreamController: Initialized successfully")
end

function StreamController.createStreamUI()
    -- Create main streaming UI
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "StreamingUI"
    screenGui.Parent = player.PlayerGui
    
    -- Stream button
    streamButton = Instance.new("TextButton")
    streamButton.Name = "StreamButton"
    streamButton.Size = UDim2.new(0, 200, 0, 60)
    streamButton.Position = UDim2.new(0, 20, 1, -80)
    streamButton.BackgroundColor3 = Color3.fromRGB(85, 170, 85)
    streamButton.Text = "Start Stream"
    streamButton.TextColor3 = Color3.new(1,1,1)
    streamButton.TextScaled = true
    streamButton.Font = Enum.Font.GothamBold
    streamButton.Parent = screenGui
    
    -- Add button styling
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = streamButton
    
    -- Stream stats display
    statsDisplay = Instance.new("Frame")
    statsDisplay.Name = "StatsDisplay"
    statsDisplay.Size = UDim2.new(0, 300, 0, 200)
    statsDisplay.Position = UDim2.new(1, -320, 0, 20)
    statsDisplay.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    statsDisplay.BackgroundTransparency = 0.1
    statsDisplay.Visible = false
    statsDisplay.Parent = screenGui
    
    local statsCorner = Instance.new("UICorner")
    statsCorner.CornerRadius = UDim.new(0, 12)
    statsCorner.Parent = statsDisplay
    
    -- Stats labels
    local statsLayout = Instance.new("UIListLayout")
    statsLayout.Padding = UDim.new(0, 5)
    statsLayout.Parent = statsDisplay
    
    local function createStatLabel(name, text)
        local label = Instance.new("TextLabel")
        label.Name = name
        label.Size = UDim2.new(1, -10, 0, 25)
        label.BackgroundTransparency = 1
        label.Text = text
        label.TextColor3 = Color3.new(1,1,1)
        label.TextScaled = true
        label.Font = Enum.Font.Gotham
        label.TextXAlignment = Enum.TextXAlignment.Left
        label.Parent = statsDisplay
        return label
    end
    
    createStatLabel("ViewersLabel", "👥 Viewers: 0")
    createStatLabel("LikesLabel", "❤️ Likes: 0")
    createStatLabel("FollowersLabel", "👥 Followers: 0")
    createStatLabel("EarningsLabel", "💰 Earnings: $0")
    createStatLabel("DurationLabel", "⏱️ Duration: 0:00")
    
    -- Streaming indicator
    streamingIndicator = Instance.new("Frame")
    streamingIndicator.Name = "StreamingIndicator"
    streamingIndicator.Size = UDim2.new(0, 150, 0, 30)
    streamingIndicator.Position = UDim2.new(0.5, -75, 0, 20)
    streamingIndicator.BackgroundColor3 = Color3.fromRGB(255, 0, 0)
    streamingIndicator.Visible = false
    streamingIndicator.Parent = screenGui
    
    local indicatorCorner = Instance.new("UICorner")
    indicatorCorner.CornerRadius = UDim.new(0, 15)
    indicatorCorner.Parent = streamingIndicator
    
    local indicatorLabel = Instance.new("TextLabel")
    indicatorLabel.Size = UDim2.new(1, 0, 1, 0)
    indicatorLabel.BackgroundTransparency = 1
    indicatorLabel.Text = "🔴 LIVE"
    indicatorLabel.TextColor3 = Color3.new(1,1,1)
    indicatorLabel.TextScaled = true
    indicatorLabel.Font = Enum.Font.GothamBold
    indicatorLabel.Parent = streamingIndicator
    
    -- Connect button events
    streamButton.MouseButton1Click:Connect(StreamController.onStreamButtonClick)
    
    streamUI = screenGui
end

function StreamController.onStreamButtonClick()
    if isStreaming then
        StreamController.stopStream()
    else
        StreamController.startStream()
    end
end

function StreamController.startStream()
    if isStreaming then return end
    
    -- Get player's current studio and equipment
    local studioType = "DIRT_STUDIO" -- TODO: Get from player data
    local equipment = {
        PC = {level = 1},
        MICROPHONE = {level = 1},
        CAMERA = {level = 1},
        CHAIR = {level = 1}
    } -- TODO: Get from player data
    
    -- Send start stream request
    EventManager.Streaming.StartStream:FireServer({
        studioType = studioType,
        equipment = equipment
    })
    
    -- Update UI immediately (optimistic)
    streamButton.Text = "Starting..."
    streamButton.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
end

function StreamController.stopStream()
    if not isStreaming then return end
    
    -- Send stop stream request
    EventManager.Streaming.StopStream:FireServer()
    
    -- Update UI immediately (optimistic)
    streamButton.Text = "Stopping..."
    streamButton.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
end

function StreamController.onStreamStartResponse(response)
    if response.success then
        isStreaming = true
        streamStartTime = tick()
        
        -- Update UI
        streamButton.Text = "Stop Stream"
        streamButton.BackgroundColor3 = Color3.fromRGB(255, 85, 85)
        statsDisplay.Visible = true
        streamingIndicator.Visible = true
        
        -- Start dummy animations
        StreamController.startDummyAnimations()
        
        -- Play start sound
        StreamController.playSound("StreamStart")
        
        -- Animate streaming indicator
        StreamController.animateStreamingIndicator()
        
        print("Stream started successfully!")
    else
        -- Handle error
        streamButton.Text = "Start Stream"
        streamButton.BackgroundColor3 = Color3.fromRGB(85, 170, 85)
        
        print("Failed to start stream:", response.reason)
        
        if response.cooldown then
            StreamController.showCooldownMessage(response.cooldown)
        end
    end
end

function StreamController.onStreamStopResponse(response)
    if response.success then
        isStreaming = false
        
        -- Update UI
        streamButton.Text = "Start Stream"
        streamButton.BackgroundColor3 = Color3.fromRGB(85, 170, 85)
        statsDisplay.Visible = false
        streamingIndicator.Visible = false
        
        -- Stop dummy animations
        StreamController.stopDummyAnimations()
        
        -- Play stop sound
        StreamController.playSound("StreamStop")
        
        -- Show final stats
        if response.finalStats then
            StreamController.showFinalStats(response.finalStats)
        end
        
        print("Stream stopped successfully!")
    else
        print("Failed to stop stream:", response.reason)
    end
end

function StreamController.onEarningsUpdate(data)
    if not isStreaming then return end
    
    -- Update stats display
    local viewers = statsDisplay:FindFirstChild("ViewersLabel")
    local likes = statsDisplay:FindFirstChild("LikesLabel")
    local followers = statsDisplay:FindFirstChild("FollowersLabel")
    local earnings = statsDisplay:FindFirstChild("EarningsLabel")
    local duration = statsDisplay:FindFirstChild("DurationLabel")
    
    if viewers then viewers.Text = "👥 Viewers: " .. data.viewers end
    if likes then likes.Text = "❤️ Likes: " .. data.totalLikes end
    if followers then followers.Text = "👥 Followers: " .. data.totalFollowers end
    if earnings then earnings.Text = "💰 Earnings: $" .. data.totalEarnings end
    if duration then 
        local mins = math.floor(data.duration / 60)
        local secs = math.floor(data.duration % 60)
        duration.Text = string.format("⏱️ Duration: %d:%02d", mins, secs)
    end
    
    currentStreamData = data
end

function StreamController.setupDummyAnimations()
    -- TODO: Setup dummy character animations
    -- This would involve creating or loading animations for:
    -- - Typing on keyboard
    -- - Talking/gesturing
    -- - Reacting to chat
    -- - Looking at camera
end

function StreamController.startDummyAnimations()
    -- TODO: Start playing dummy animations
    print("Starting dummy animations...")
end

function StreamController.stopDummyAnimations()
    -- TODO: Stop dummy animations
    print("Stopping dummy animations...")
end

function StreamController.animateStreamingIndicator()
    if not streamingIndicator then return end
    
    local tween = TweenService:Create(
        streamingIndicator,
        TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
        {BackgroundTransparency = 0.3}
    )
    tween:Play()
end

function StreamController.playSound(soundName)
    -- TODO: Play appropriate sound effects
    print("Playing sound:", soundName)
end

function StreamController.showCooldownMessage(cooldown)
    -- TODO: Show cooldown notification
    print("Cooldown remaining:", math.ceil(cooldown), "seconds")
end

function StreamController.showFinalStats(stats)
    -- TODO: Create and show final stats popup
    print("Final stream stats:", stats)
end

function StreamController.getStreamStatus()
    return {
        isStreaming = isStreaming,
        data = currentStreamData,
        startTime = streamStartTime
    }
end

function StreamController.cleanup()
    if streamUI then
        streamUI:Destroy()
        streamUI = nil
    end
    
    isStreaming = false
    currentStreamData = {}
    
    print("StreamController: Cleaned up")
end

return StreamController
