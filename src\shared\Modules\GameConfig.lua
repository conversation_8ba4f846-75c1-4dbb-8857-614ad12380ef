local GameConfig = {}

-- Studio types and their configurations
GameConfig.StudioTypes = {
    DIRT_STUDIO = {
        id = 1,
        name = "Dirt Studio",
        price = 0,
        baseViewers = 5,
        maxEquipmentSlots = 4,
        unlockLevel = 1,
        followersRequired = 0,
        theme = "Basic",
        description = "A humble beginning for aspiring streamers"
    },
    WOODEN_CABIN = {
        id = 2,
        name = "Wooden Cabin",
        price = 5000,
        baseViewers = 15,
        maxEquipmentSlots = 6,
        unlockLevel = 5,
        followersRequired = 100,
        theme = "Rustic",
        description = "Cozy cabin vibes for nature-loving streamers"
    },
    GAMER_APARTMENT = {
        id = 3,
        name = "Gamer Apartment",
        price = 25000,
        baseViewers = 35,
        maxEquipmentSlots = 8,
        unlockLevel = 10,
        followersRequired = 500,
        theme = "Gaming",
        description = "RGB-lit paradise for gaming content creators"
    },
    LUXURY_FLAT = {
        id = 4,
        name = "Luxury Flat",
        price = 75000,
        baseViewers = 60,
        maxEquipmentSlots = 10,
        unlockLevel = 15,
        followersRequired = 1500,
        theme = "Elegant",
        description = "High-end apartment for sophisticated streamers"
    },
    MODERN_MANSION = {
        id = 5,
        name = "Modern Mansion",
        price = 200000,
        baseViewers = 100,
        maxEquipmentSlots = 12,
        unlockLevel = 25,
        followersRequired = 5000,
        theme = "Luxury",
        description = "Spacious mansion for streaming royalty"
    },
    SPACE_POD = {
        id = 6,
        name = "Space Pod",
        price = 500000,
        baseViewers = 150,
        maxEquipmentSlots = 15,
        unlockLevel = 35,
        followersRequired = 15000,
        theme = "Futuristic",
        description = "Out-of-this-world streaming experience"
    },
    NEON_TOWER = {
        id = 7,
        name = "Neon Tower",
        price = 1000000,
        baseViewers = 250,
        maxEquipmentSlots = 18,
        unlockLevel = 50,
        followersRequired = 50000,
        theme = "Cyberpunk",
        description = "Cyberpunk aesthetic for tech streamers"
    },
    TECH_BUNKER = {
        id = 8,
        name = "Secret Tech Bunker",
        price = 2500000,
        baseViewers = 400,
        maxEquipmentSlots = 25,
        unlockLevel = 75,
        followersRequired = 150000,
        theme = "Underground",
        description = "Ultimate secret laboratory for elite streamers"
    }
}

-- Equipment types and their stats
GameConfig.EquipmentTypes = {
    PC = {
        category = "Essential",
        baseViewerMultiplier = 1.2,
        description = "The heart of your streaming setup",
        levels = {
            {name = "Basic PC", price = 100, multiplier = 1.0, description = "Entry-level computer"},
            {name = "Gaming PC", price = 500, multiplier = 1.5, description = "Mid-range gaming computer"},
            {name = "Streaming Beast", price = 2000, multiplier = 2.0, description = "High-end streaming machine"},
            {name = "Pro Workstation", price = 8000, multiplier = 2.8, description = "Professional workstation"},
            {name = "Quantum Computer", price = 25000, multiplier = 4.0, description = "Futuristic quantum processor"}
        }
    },
    MICROPHONE = {
        category = "Audio",
        baseViewerMultiplier = 1.15,
        description = "Crystal clear audio for your audience",
        levels = {
            {name = "Basic Mic", price = 50, multiplier = 1.2, description = "Simple USB microphone"},
            {name = "Studio Mic", price = 200, multiplier = 1.5, description = "Professional studio microphone"},
            {name = "Broadcast Mic", price = 1000, multiplier = 2.0, description = "Radio-quality microphone"},
            {name = "Podcast Pro", price = 3500, multiplier = 2.6, description = "Premium podcast microphone"},
            {name = "Holographic Mic", price = 12000, multiplier = 3.5, description = "Futuristic holographic audio"}
        }
    },
    CAMERA = {
        category = "Video",
        baseViewerMultiplier = 1.1,
        description = "Show your face in high definition",
        levels = {
            {name = "Webcam", price = 75, multiplier = 1.1, description = "Basic webcam"},
            {name = "HD Camera", price = 300, multiplier = 1.4, description = "High-definition camera"},
            {name = "4K Camera", price = 1200, multiplier = 1.8, description = "Ultra HD 4K camera"},
            {name = "Cinema Camera", price = 4500, multiplier = 2.4, description = "Professional cinema camera"},
            {name = "Hologram Projector", price = 15000, multiplier = 3.2, description = "3D holographic projection"}
        }
    },
    CHAIR = {
        category = "Comfort",
        baseViewerMultiplier = 1.05,
        description = "Comfort for those long streaming sessions",
        levels = {
            {name = "Office Chair", price = 40, multiplier = 1.1, description = "Basic office chair"},
            {name = "Gaming Chair", price = 150, multiplier = 1.3, description = "Ergonomic gaming chair"},
            {name = "Executive Chair", price = 600, multiplier = 1.6, description = "Luxury executive chair"},
            {name = "Massage Chair", price = 2200, multiplier = 2.1, description = "Chair with built-in massage"},
            {name = "Zero-G Pod", price = 8500, multiplier = 2.8, description = "Anti-gravity relaxation pod"}
        }
    },
    LIGHTING = {
        category = "Visual",
        baseViewerMultiplier = 1.08,
        description = "Perfect lighting for professional streams",
        levels = {
            {name = "Desk Lamp", price = 25, multiplier = 1.1, description = "Simple desk lighting"},
            {name = "Ring Light", price = 100, multiplier = 1.3, description = "Professional ring light"},
            {name = "Studio Lights", price = 450, multiplier = 1.7, description = "Full studio lighting kit"},
            {name = "Smart RGB Setup", price = 1800, multiplier = 2.2, description = "Programmable RGB lighting"},
            {name = "Holographic Ambience", price = 6500, multiplier = 3.0, description = "Immersive holographic lighting"}
        }
    },
    DESK = {
        category = "Furniture",
        baseViewerMultiplier = 1.03,
        description = "Organize your streaming workspace",
        levels = {
            {name = "Basic Desk", price = 60, multiplier = 1.05, description = "Simple wooden desk"},
            {name = "Gaming Desk", price = 250, multiplier = 1.2, description = "Ergonomic gaming desk"},
            {name = "Standing Desk", price = 800, multiplier = 1.4, description = "Adjustable standing desk"},
            {name = "Smart Desk", price = 2800, multiplier = 1.8, description = "Tech-integrated smart desk"},
            {name = "Floating Platform", price = 9500, multiplier = 2.5, description = "Anti-gravity floating workspace"}
        }
    }
}

-- Currency configurations
GameConfig.Currencies = {
    CASH = {
        name = "Cash",
        symbol = "$",
        color = Color3.fromRGB(85, 170, 85),
        description = "Primary currency for equipment and studios"
    },
    FOLLOWERS = {
        name = "Followers",
        symbol = "👥",
        color = Color3.fromRGB(255, 100, 100),
        description = "Social currency for unlocks and prestige"
    },
    GEMS = {
        name = "Gems",
        symbol = "💎",
        color = Color3.fromRGB(100, 150, 255),
        description = "Premium currency from events and purchases"
    },
    CLOUT = {
        name = "Clout",
        symbol = "⭐",
        color = Color3.fromRGB(255, 215, 0),
        description = "Prestige currency for exclusive items"
    }
}

-- Game economy settings
GameConfig.Economy = {
    -- Base streaming income
    BaseViewerIncome = 1,
    BaseLikeChance = 0.2, -- 20% chance per viewer to like
    BaseFollowChance = 0.05, -- 5% chance per viewer to follow
    BaseCashPerViewer = 0.1,
    BaseCloutPerStream = 1,

    -- Progression multipliers
    ViewerFalloffRate = 0.1, -- 10% of viewers leave every interval if not engaged
    StreamLengthMultiplier = 1.2, -- Bonus for longer streams
    EquipmentSynergyBonus = 0.1, -- Bonus for having multiple high-tier equipment

    -- Level progression
    BaseXPPerStream = 10,
    XPMultiplierPerLevel = 1.1,
    LevelUpXPRequirement = 100, -- Base XP needed for level 2
    LevelUpXPMultiplier = 1.5, -- XP requirement multiplier per level

    -- Daily rewards
    DailyLoginBonus = {
        cash = 100,
        gems = 1,
        clout = 5
    },

    -- Stream challenges
    ChallengeRewards = {
        {name = "First Stream", requirement = 1, reward = {cash = 50, gems = 1}},
        {name = "100 Viewers", requirement = 100, reward = {cash = 200, gems = 2}},
        {name = "1K Followers", requirement = 1000, reward = {cash = 1000, gems = 5}},
        {name = "10K Followers", requirement = 10000, reward = {cash = 5000, gems = 10}},
        {name = "100K Followers", requirement = 100000, reward = {cash = 25000, gems = 25}}
    }
}

-- Gamepass configurations
GameConfig.Gamepasses = {
    AUTO_STREAM = {
        id = 0, -- Replace with actual product ID
        name = "Auto Stream",
        price = 199, -- Robux
        description = "Automatically starts streams every few seconds",
        benefits = {"Passive streaming", "No manual clicking required"}
    },
    DOUBLE_MULTIPLIER = {
        id = 0,
        name = "Double Views & Likes",
        price = 299,
        description = "2x views, likes, and earnings from all streams",
        benefits = {"2x viewer growth", "2x earnings", "2x experience"}
    },
    STARTER_PACK = {
        id = 0,
        name = "Instant $100K Pack",
        price = 399,
        description = "Instant cash boost for new streamers",
        benefits = {"$100,000 cash", "500 gems", "1000 clout"}
    },
    ALL_HOUSES = {
        id = 0,
        name = "Unlock All Houses",
        price = 799,
        description = "Immediate access to all 8 studio types",
        benefits = {"All studios unlocked", "No level requirements", "Exclusive themes"}
    },
    PRO_SETUP = {
        id = 0,
        name = "Advanced Setup Pack",
        price = 499,
        description = "Exclusive high-tier streaming equipment",
        benefits = {"Unique equipment", "Higher multipliers", "Special effects"}
    },
    CUSTOM_COLORS = {
        id = 0,
        name = "Custom House Colors",
        price = 149,
        description = "Personalize your studio with custom colors",
        benefits = {"Color customization", "Unique themes", "Personal branding"}
    },
    LAYOUT_SAVER = {
        id = 0,
        name = "Save Multiple Layouts",
        price = 249,
        description = "Save and load different studio configurations",
        benefits = {"Multiple layouts", "Quick switching", "Backup configurations"}
    },
    VIP_ACCESS = {
        id = 0,
        name = "VIP Chest Access",
        price = 599,
        description = "Access to exclusive VIP areas and items",
        benefits = {"VIP areas", "Exclusive items", "Early event access"}
    }
}

-- Game settings
GameConfig.Settings = {
    -- Game info
    Version = "1.0.0",
    GameName = "Grown A Streamer",

    -- Core gameplay
    AutoSaveInterval = 60, -- seconds
    StreamTickRate = 5, -- seconds between stream updates
    MaxStreamLength = 3600, -- seconds (1 hour)
    MinStreamLength = 10, -- minimum stream duration

    -- Plot system
    TotalPlots = 8,
    PlotAssignmentTimeout = 30, -- seconds to assign plot on join
    PlotLiberationDelay = 5, -- seconds after player leaves

    -- Placement system
    MaxPlacementDistance = 50, -- studs
    PlacementRotationStep = 15, -- degrees
    PlacementValidationRadius = 5, -- studs

    -- Social features
    MaxStudioVisitors = 10,
    RatingCooldown = 300, -- seconds between ratings
    CloutSendCooldown = 60, -- seconds between clout sends

    -- Performance
    MaxRenderDistance = 200, -- studs
    LODUpdateRate = 2, -- seconds
    GarbageCollectionInterval = 120 -- seconds
}

-- Event configurations
GameConfig.Events = {
    SEASONAL = {
        CHRISTMAS = {
            name = "Christmas Special",
            duration = 14, -- days
            bonusMultiplier = 1.5,
            specialItems = {"Christmas Lights", "Santa Hat", "Holiday Mic"},
            rewards = {cash = 1000, gems = 10, clout = 50}
        },
        HALLOWEEN = {
            name = "Spooky Stream",
            duration = 7,
            bonusMultiplier = 1.3,
            specialItems = {"Pumpkin Light", "Ghost Camera", "Spooky Chair"},
            rewards = {cash = 750, gems = 7, clout = 35}
        },
        SUMMER = {
            name = "Summer Vibes",
            duration = 21,
            bonusMultiplier = 1.2,
            specialItems = {"Beach Setup", "Tropical Lights", "Summer Mic"},
            rewards = {cash = 500, gems = 5, clout = 25}
        }
    },

    SPONSORED = {
        BLOX_COLA = {
            name = "Blox-Cola Partnership",
            duration = 3,
            bonusMultiplier = 1.4,
            specialItems = {"Blox-Cola Fridge", "Branded Mic", "Cola Lights"},
            requirements = {followers = 1000}
        },
        GAMER_BOX = {
            name = "GamerBox Sponsorship",
            duration = 5,
            bonusMultiplier = 1.6,
            specialItems = {"GamerBox PC", "Branded Chair", "RGB Setup"},
            requirements = {followers = 5000}
        }
    },

    CHALLENGES = {
        SPEED_STREAM = {
            name = "Speed Streaming Challenge",
            duration = 1, -- hours
            goal = "Reach 1000 viewers in 10 minutes",
            reward = {cash = 2000, gems = 15, clout = 100}
        },
        MARATHON = {
            name = "Streaming Marathon",
            duration = 24,
            goal = "Stream for 6 hours total",
            reward = {cash = 5000, gems = 25, clout = 200}
        }
    }
}

-- Utility functions
function GameConfig.getStudioById(id)
    for studioType, config in pairs(GameConfig.StudioTypes) do
        if config.id == id then
            return studioType, config
        end
    end
    return nil
end

function GameConfig.getStudioByName(name)
    for studioType, config in pairs(GameConfig.StudioTypes) do
        if config.name == name then
            return studioType, config
        end
    end
    return nil
end

function GameConfig.getEquipmentLevel(equipmentType, level)
    local equipment = GameConfig.EquipmentTypes[equipmentType]
    if equipment and equipment.levels[level] then
        return equipment.levels[level]
    end
    return nil
end

function GameConfig.calculateStreamEarnings(baseViewers, equipmentMultipliers, studioMultiplier, streamDuration)
    local totalMultiplier = studioMultiplier or 1

    -- Apply equipment multipliers
    for _, multiplier in pairs(equipmentMultipliers or {}) do
        totalMultiplier = totalMultiplier * multiplier
    end

    -- Apply stream duration bonus
    local durationBonus = 1 + (streamDuration / 3600) * GameConfig.Economy.StreamLengthMultiplier

    local finalViewers = math.floor(baseViewers * totalMultiplier * durationBonus)
    local cashEarned = finalViewers * GameConfig.Economy.BaseCashPerViewer
    local likesEarned = math.floor(finalViewers * GameConfig.Economy.BaseLikeChance)
    local followersEarned = math.floor(finalViewers * GameConfig.Economy.BaseFollowChance)

    return {
        viewers = finalViewers,
        cash = cashEarned,
        likes = likesEarned,
        followers = followersEarned,
        clout = GameConfig.Economy.BaseCloutPerStream
    }
end

function GameConfig.getXPRequiredForLevel(level)
    if level <= 1 then return 0 end
    local baseXP = GameConfig.Economy.LevelUpXPRequirement
    return math.floor(baseXP * (GameConfig.Economy.LevelUpXPMultiplier ^ (level - 2)))
end

function GameConfig.isStudioUnlocked(playerLevel, playerFollowers, studioType)
    local studio = GameConfig.StudioTypes[studioType]
    if not studio then return false end

    return playerLevel >= studio.unlockLevel and playerFollowers >= studio.followersRequired
end

function GameConfig.getGamepassById(productId)
    for gamepassType, config in pairs(GameConfig.Gamepasses) do
        if config.id == productId then
            return gamepassType, config
        end
    end
    return nil
end

-- Validation functions
function GameConfig.validatePlacement(position, studioType, plotBounds)
    -- Add placement validation logic here
    -- Check if position is within plot bounds
    -- Check if position is on a valid placement zone
    -- Check for collisions with existing items
    return true -- Placeholder
end

function GameConfig.validatePurchase(playerData, itemType, itemLevel)
    -- Add purchase validation logic here
    -- Check if player has enough currency
    -- Check if item is unlocked
    -- Check level requirements
    return true -- Placeholder
end

return GameConfig
