-- DataEvents.lua
-- RemoteEvents for data management and player progression

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local DataEvents = {}

-- Create RemoteEvents for data management
local function createRemoteEvent(name)
    local event = Instance.new("RemoteEvent")
    event.Name = name
    event.Parent = ReplicatedStorage
    return event
end

local function createRemoteFunction(name)
    local func = Instance.new("RemoteFunction")
    func.Name = name
    func.Parent = ReplicatedStorage
    return func
end

-- Data Events
DataEvents.PlayerDataLoaded = createRemoteEvent("PlayerDataLoaded")
DataEvents.PlayerDataSaved = createRemoteEvent("PlayerDataSaved")
DataEvents.PlayerStatsUpdate = createRemoteEvent("PlayerStatsUpdate")
DataEvents.PlayerLevelUp = createRemoteEvent("PlayerLevelUp")
DataEvents.AchievementUnlocked = createRemoteEvent("AchievementUnlocked")
DataEvents.DailyRewardClaimed = createRemoteEvent("DailyRewardClaimed")

-- Data Functions
DataEvents.GetPlayerData = createRemoteFunction("GetPlayerData")
DataEvents.GetPlayerStats = createRemoteFunction("GetPlayerStats")
DataEvents.GetAchievements = createRemoteFunction("GetAchievements")
DataEvents.GetDailyRewards = createRemoteFunction("GetDailyRewards")
DataEvents.CanClaimDailyReward = createRemoteFunction("CanClaimDailyReward")

-- Settings Events
DataEvents.UpdateSettings = createRemoteEvent("UpdateSettings")
DataEvents.GetSettings = createRemoteFunction("GetSettings")

-- Plot Events
DataEvents.PlotAssigned = createRemoteEvent("PlotAssigned")
DataEvents.PlotLiberated = createRemoteEvent("PlotLiberated")
DataEvents.TeleportToPlot = createRemoteEvent("TeleportToPlot")

-- Plot Functions
DataEvents.GetPlayerPlot = createRemoteFunction("GetPlayerPlot")
DataEvents.GetAvailablePlots = createRemoteFunction("GetAvailablePlots")

-- Event descriptions
DataEvents.EventDescriptions = {
    PlayerDataLoaded = "Fired when player data is successfully loaded",
    PlayerDataSaved = "Fired when player data is saved",
    PlayerStatsUpdate = "Fired when player statistics are updated",
    PlayerLevelUp = "Fired when player levels up",
    AchievementUnlocked = "Fired when player unlocks an achievement",
    DailyRewardClaimed = "Fired when player claims daily reward",
    GetPlayerData = "Function to get complete player data",
    GetPlayerStats = "Function to get player statistics",
    GetAchievements = "Function to get player achievements",
    GetDailyRewards = "Function to get available daily rewards",
    CanClaimDailyReward = "Function to check if daily reward can be claimed",
    UpdateSettings = "Fired when player updates game settings",
    GetSettings = "Function to get player settings",
    PlotAssigned = "Fired when player is assigned a plot",
    PlotLiberated = "Fired when a plot becomes available",
    TeleportToPlot = "Fired when player teleports to their plot",
    GetPlayerPlot = "Function to get player's assigned plot",
    GetAvailablePlots = "Function to get list of available plots"
}

return DataEvents
