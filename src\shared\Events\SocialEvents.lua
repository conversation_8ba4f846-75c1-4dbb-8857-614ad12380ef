-- SocialEvents.lua
-- RemoteEvents for social features and multiplayer interactions

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local SocialEvents = {}

-- Create RemoteEvents for social features
local function createRemoteEvent(name)
    local event = Instance.new("RemoteEvent")
    event.Name = name
    event.Parent = ReplicatedStorage
    return event
end

local function createRemoteFunction(name)
    local func = Instance.new("RemoteFunction")
    func.Name = name
    func.Parent = ReplicatedStorage
    return func
end

-- Social Events
SocialEvents.VisitStudio = createRemoteEvent("VisitStudio")
SocialEvents.LeaveStudio = createRemoteEvent("LeaveStudio")
SocialEvents.RateStudio = createRemoteEvent("RateStudio")
SocialEvents.SendClout = createRemoteEvent("SendClout")
SocialEvents.UpdateLeaderboard = createRemoteEvent("UpdateLeaderboard")
SocialEvents.PlayerJoinedStudio = createRemoteEvent("PlayerJoinedStudio")
SocialEvents.PlayerLeftStudio = createRemoteEvent("PlayerLeftStudio")

-- Social Functions
SocialEvents.GetStudioList = createRemoteFunction("GetStudioList")
SocialEvents.GetStudioInfo = createRemoteFunction("GetStudioInfo")
SocialEvents.GetLeaderboard = createRemoteFunction("GetLeaderboard")
SocialEvents.GetPlayerRating = createRemoteFunction("GetPlayerRating")
SocialEvents.CanVisitStudio = createRemoteFunction("CanVisitStudio")
SocialEvents.GetStudioVisitors = createRemoteFunction("GetStudioVisitors")

-- Friend System Events
SocialEvents.SendFriendRequest = createRemoteEvent("SendFriendRequest")
SocialEvents.AcceptFriendRequest = createRemoteEvent("AcceptFriendRequest")
SocialEvents.DeclineFriendRequest = createRemoteEvent("DeclineFriendRequest")
SocialEvents.RemoveFriend = createRemoteEvent("RemoveFriend")

-- Friend System Functions
SocialEvents.GetFriendsList = createRemoteFunction("GetFriendsList")
SocialEvents.GetFriendRequests = createRemoteFunction("GetFriendRequests")

-- Event descriptions
SocialEvents.EventDescriptions = {
    VisitStudio = "Fired when player visits another studio",
    LeaveStudio = "Fired when player leaves a studio",
    RateStudio = "Fired when player rates a studio",
    SendClout = "Fired when player sends clout to another player",
    UpdateLeaderboard = "Fired to update global leaderboard",
    PlayerJoinedStudio = "Fired when a player joins your studio",
    PlayerLeftStudio = "Fired when a player leaves your studio",
    GetStudioList = "Function to get list of visitable studios",
    GetStudioInfo = "Function to get detailed studio information",
    GetLeaderboard = "Function to get current leaderboard data",
    GetPlayerRating = "Function to get player's studio rating",
    CanVisitStudio = "Function to check if player can visit a studio",
    GetStudioVisitors = "Function to get current studio visitors"
}

return SocialEvents
