-- EventManager.lua
-- Central manager for all RemoteEvents and RemoteFunctions

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import all event modules
local StreamingEvents = require(script.Parent.StreamingEvents)
local PlacementEvents = require(script.Parent.PlacementEvents)
local ShopEvents = require(script.Parent.ShopEvents)
local SocialEvents = require(script.Parent.SocialEvents)
local DataEvents = require(script.Parent.DataEvents)

local EventManager = {}

-- Combine all events into a single interface
EventManager.Streaming = StreamingEvents
EventManager.Placement = PlacementEvents
EventManager.Shop = ShopEvents
EventManager.Social = SocialEvents
EventManager.Data = DataEvents

-- Event categories for organization
EventManager.Categories = {
    STREAMING = "Streaming",
    PLACEMENT = "Placement", 
    SHOP = "Shop",
    SOCIAL = "Social",
    DATA = "Data"
}

-- Rate limiting for events (to prevent spam)
EventManager.RateLimits = {
    -- Streaming events
    StartStream = {cooldown = 5, lastUsed = {}},
    StopStream = {cooldown = 1, lastUsed = {}},
    
    -- Placement events
    ConfirmPlacement = {cooldown = 0.5, lastUsed = {}},
    RotateItem = {cooldown = 0.1, lastUsed = {}},
    DeleteItem = {cooldown = 1, lastUsed = {}},
    
    -- Shop events
    PurchaseItem = {cooldown = 1, lastUsed = {}},
    PurchaseStudio = {cooldown = 2, lastUsed = {}},
    
    -- Social events
    RateStudio = {cooldown = 300, lastUsed = {}}, -- 5 minutes
    SendClout = {cooldown = 60, lastUsed = {}}, -- 1 minute
    VisitStudio = {cooldown = 5, lastUsed = {}},
    
    -- Data events
    TeleportToPlot = {cooldown = 10, lastUsed = {}}
}

-- Utility functions
function EventManager.checkRateLimit(eventName, player)
    local limit = EventManager.RateLimits[eventName]
    if not limit then return true end
    
    local userId = player.UserId
    local lastUsed = limit.lastUsed[userId] or 0
    local currentTime = tick()
    
    if currentTime - lastUsed >= limit.cooldown then
        limit.lastUsed[userId] = currentTime
        return true
    end
    
    return false
end

function EventManager.getRemainingCooldown(eventName, player)
    local limit = EventManager.RateLimits[eventName]
    if not limit then return 0 end
    
    local userId = player.UserId
    local lastUsed = limit.lastUsed[userId] or 0
    local currentTime = tick()
    local remaining = limit.cooldown - (currentTime - lastUsed)
    
    return math.max(0, remaining)
end

-- Event logging for debugging
EventManager.EventLog = {}
EventManager.MaxLogEntries = 1000

function EventManager.logEvent(eventName, player, data)
    local logEntry = {
        timestamp = tick(),
        eventName = eventName,
        playerName = player.Name,
        playerId = player.UserId,
        data = data
    }
    
    table.insert(EventManager.EventLog, logEntry)
    
    -- Keep log size manageable
    if #EventManager.EventLog > EventManager.MaxLogEntries then
        table.remove(EventManager.EventLog, 1)
    end
end

function EventManager.getEventLog(filterPlayer, filterEvent, maxEntries)
    local filteredLog = {}
    local count = 0
    maxEntries = maxEntries or 50
    
    -- Iterate backwards through log (newest first)
    for i = #EventManager.EventLog, 1, -1 do
        local entry = EventManager.EventLog[i]
        
        -- Apply filters
        if (not filterPlayer or entry.playerId == filterPlayer.UserId) and
           (not filterEvent or entry.eventName == filterEvent) then
            table.insert(filteredLog, entry)
            count = count + 1
            
            if count >= maxEntries then
                break
            end
        end
    end
    
    return filteredLog
end

-- Event validation
function EventManager.validateEventData(eventName, data)
    -- Add specific validation rules for different events
    local validationRules = {
        StartStream = function(d)
            return type(d) == "table" and d.studioType and d.equipment
        end,
        
        ConfirmPlacement = function(d)
            return type(d) == "table" and d.itemType and d.position and d.rotation
        end,
        
        PurchaseItem = function(d)
            return type(d) == "table" and d.itemType and d.level and type(d.price) == "number"
        end,
        
        RateStudio = function(d)
            return type(d) == "table" and d.targetPlayer and type(d.rating) == "number" and 
                   d.rating >= 1 and d.rating <= 5
        end
    }
    
    local validator = validationRules[eventName]
    if validator then
        return validator(data)
    end
    
    return true -- No specific validation rule
end

-- Initialize all events
function EventManager.initialize()
    print("EventManager: Initializing all RemoteEvents and RemoteFunctions...")
    
    -- Count total events
    local totalEvents = 0
    for category, events in pairs({
        StreamingEvents, PlacementEvents, ShopEvents, SocialEvents, DataEvents
    }) do
        for eventName, event in pairs(events) do
            if typeof(event) == "Instance" then
                totalEvents = totalEvents + 1
            end
        end
    end
    
    print("EventManager: Initialized", totalEvents, "events across 5 categories")
    return true
end

-- Cleanup function
function EventManager.cleanup()
    -- Clear rate limit data
    for eventName, limit in pairs(EventManager.RateLimits) do
        limit.lastUsed = {}
    end
    
    -- Clear event log
    EventManager.EventLog = {}
    
    print("EventManager: Cleaned up rate limits and event log")
end

return EventManager
