-- PlayerManager.lua
-- Manages player joining, leaving, and data coordination

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)
local DataManager = require(ReplicatedStorage.Shared.Modules.DataManager)

-- Import other systems
local PlotManager = require(script.Parent.PlotManager)
local StreamingSystem = require(script.Parent.Parent.Systems.StreamingSystem)
local PlacementSystem = require(script.Parent.Parent.Systems.PlacementSystem)
local ShopHandler = require(script.Parent.Parent.Handlers.ShopHandler)

local PlayerManager = {}

-- Player data tracking
local playerDataManagers = {} -- [userId] = DataManager instance
local playerSessions = {} -- [userId] = {joinTime, plotId, isActive}

function PlayerManager.initialize()
    print("PlayerManager: Initializing...")
    
    -- Connect player events
    Players.PlayerAdded:Connect(PlayerManager.onPlayerAdded)
    Players.PlayerRemoving:Connect(PlayerManager.onPlayerRemoving)
    
    -- Connect data events
    EventManager.Data.GetPlayerData.OnServerInvoke = PlayerManager.getPlayerData
    EventManager.Data.GetPlayerStats.OnServerInvoke = PlayerManager.getPlayerStats
    EventManager.Data.GetAchievements.OnServerInvoke = PlayerManager.getAchievements
    EventManager.Data.GetDailyRewards.OnServerInvoke = PlayerManager.getDailyRewards
    EventManager.Data.CanClaimDailyReward.OnServerInvoke = PlayerManager.canClaimDailyReward
    EventManager.Data.DailyRewardClaimed.OnServerEvent:Connect(PlayerManager.onDailyRewardClaimed)
    
    print("PlayerManager: Initialized successfully")
end

function PlayerManager.onPlayerAdded(player)
    print("PlayerManager: Player", player.Name, "joined")
    
    -- Create data manager
    local dataManager = DataManager.new(player)
    playerDataManagers[player.UserId] = dataManager
    
    -- Create player session
    playerSessions[player.UserId] = {
        joinTime = tick(),
        plotId = nil,
        isActive = true,
        lastActivity = tick()
    }
    
    -- Set up leaderstats
    PlayerManager.setupLeaderstats(player, dataManager)
    
    -- Wait for character to spawn, then initialize player
    if player.Character then
        PlayerManager.initializePlayer(player, dataManager)
    else
        player.CharacterAdded:Connect(function()
            PlayerManager.initializePlayer(player, dataManager)
        end)
    end
end

function PlayerManager.onPlayerRemoving(player)
    print("PlayerManager: Player", player.Name, "leaving")
    
    -- Clean up data manager
    local dataManager = playerDataManagers[player.UserId]
    if dataManager then
        dataManager:destroy()
        playerDataManagers[player.UserId] = nil
    end
    
    -- Clean up session
    playerSessions[player.UserId] = nil
    
    -- Notify other systems
    -- PlotManager will handle plot liberation automatically
end

function PlayerManager.setupLeaderstats(player, dataManager)
    local leaderstats = Instance.new("Folder")
    leaderstats.Name = "leaderstats"
    leaderstats.Parent = player
    
    local playerData = dataManager:getData()
    
    -- Cash
    local cash = Instance.new("IntValue")
    cash.Name = "Cash"
    cash.Value = playerData.cash or 0
    cash.Parent = leaderstats
    
    -- Level
    local level = Instance.new("IntValue")
    level.Name = "Level"
    level.Value = playerData.level or 1
    level.Parent = leaderstats
    
    -- Followers
    local followers = Instance.new("IntValue")
    followers.Name = "Followers"
    followers.Value = playerData.stats.totalFollowers or 0
    followers.Parent = leaderstats
    
    -- Views
    local views = Instance.new("IntValue")
    views.Name = "Views"
    views.Value = playerData.stats.totalViews or 0
    views.Parent = leaderstats
    
    -- Update leaderstats when data changes
    PlayerManager.updateLeaderstats(player, dataManager)
end

function PlayerManager.updateLeaderstats(player, dataManager)
    local leaderstats = player:FindFirstChild("leaderstats")
    if not leaderstats then return end
    
    local playerData = dataManager:getData()
    
    local cash = leaderstats:FindFirstChild("Cash")
    if cash then cash.Value = playerData.cash or 0 end
    
    local level = leaderstats:FindFirstChild("Level")
    if level then level.Value = playerData.level or 1 end
    
    local followers = leaderstats:FindFirstChild("Followers")
    if followers then followers.Value = playerData.stats.totalFollowers or 0 end
    
    local views = leaderstats:FindFirstChild("Views")
    if views then views.Value = playerData.stats.totalViews or 0 end
end

function PlayerManager.initializePlayer(player, dataManager)
    -- Send player data to client
    local playerData = dataManager:getData()
    
    EventManager.Data.PlayerDataLoaded:FireClient(player, {
        cash = playerData.cash,
        gems = playerData.gems,
        clout = playerData.clout or 0,
        level = playerData.level,
        xp = playerData.xp,
        currentStudio = playerData.currentStudio,
        ownedStudios = playerData.ownedStudios,
        equipment = playerData.equipment,
        stats = playerData.stats,
        achievements = playerData.achievements or {},
        settings = playerData.settings
    })
    
    -- Check for daily rewards
    if dataManager:canClaimDailyReward() then
        EventManager.Data.DailyRewardClaimed:FireClient(player, {
            canClaim = true,
            streak = playerData.dailyRewards.streak or 0
        })
    end
    
    -- Check for achievements
    PlayerManager.checkAchievements(player, dataManager)
    
    print("PlayerManager: Initialized player", player.Name)
end

function PlayerManager.checkAchievements(player, dataManager)
    local playerData = dataManager:getData()
    
    -- Check various achievement conditions
    local achievements = {
        {id = "first_stream", condition = playerData.stats.streamsCompleted >= 1},
        {id = "100_followers", condition = playerData.stats.totalFollowers >= 100},
        {id = "1k_followers", condition = playerData.stats.totalFollowers >= 1000},
        {id = "10k_followers", condition = playerData.stats.totalFollowers >= 10000},
        {id = "level_10", condition = playerData.level >= 10},
        {id = "level_25", condition = playerData.level >= 25},
        {id = "level_50", condition = playerData.level >= 50},
        {id = "big_spender", condition = playerData.stats.totalSpent >= 100000},
        {id = "social_butterfly", condition = playerData.stats.socialInteractions >= 50}
    }
    
    for _, achievement in ipairs(achievements) do
        if achievement.condition and not dataManager:hasAchievement(achievement.id) then
            dataManager:unlockAchievement(achievement.id)
            
            EventManager.Data.AchievementUnlocked:FireClient(player, {
                achievementId = achievement.id,
                unlockedTime = tick()
            })
        end
    end
end

function PlayerManager.getPlayerData(player)
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then return nil end
    
    return dataManager:getData()
end

function PlayerManager.getPlayerStats(player)
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then return {} end
    
    local playerData = dataManager:getData()
    return playerData.stats or {}
end

function PlayerManager.getAchievements(player)
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then return {} end
    
    local playerData = dataManager:getData()
    return playerData.achievements or {}
end

function PlayerManager.getDailyRewards(player)
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then return {} end
    
    local playerData = dataManager:getData()
    return playerData.dailyRewards or {}
end

function PlayerManager.canClaimDailyReward(player)
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then return false end
    
    return dataManager:canClaimDailyReward()
end

function PlayerManager.onDailyRewardClaimed(player)
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then return end
    
    if dataManager:claimDailyReward() then
        local playerData = dataManager:getData()
        
        -- Calculate rewards
        local rewards = GameConfig.Economy.DailyLoginBonus
        local streak = playerData.dailyRewards.streak or 1
        local streakMultiplier = math.min(streak, 7)
        
        local totalRewards = {
            cash = rewards.cash * streakMultiplier,
            gems = rewards.gems * streakMultiplier,
            clout = rewards.clout * streakMultiplier,
            streak = streak
        }
        
        -- Notify client
        EventManager.Data.DailyRewardClaimed:FireClient(player, {
            success = true,
            rewards = totalRewards,
            newStreak = streak
        })
        
        -- Update leaderstats
        PlayerManager.updateLeaderstats(player, dataManager)
        
        print(player.Name, "claimed daily reward with", streak, "day streak")
    else
        EventManager.Data.DailyRewardClaimed:FireClient(player, {
            success = false,
            reason = "Already claimed today"
        })
    end
end

function PlayerManager.getDataManager(player)
    return playerDataManagers[player.UserId]
end

function PlayerManager.updatePlayerActivity(player)
    local session = playerSessions[player.UserId]
    if session then
        session.lastActivity = tick()
    end
end

function PlayerManager.getPlayerSession(player)
    return playerSessions[player.UserId]
end

function PlayerManager.getAllActivePlayers()
    local activePlayers = {}
    for userId, session in pairs(playerSessions) do
        if session.isActive then
            local player = Players:GetPlayerByUserId(userId)
            if player then
                table.insert(activePlayers, {
                    player = player,
                    session = session
                })
            end
        end
    end
    return activePlayers
end

function PlayerManager.getPlayerCount()
    local count = 0
    for _, session in pairs(playerSessions) do
        if session.isActive then
            count = count + 1
        end
    end
    return count
end

function PlayerManager.cleanup()
    -- Clean up all data managers
    for userId, dataManager in pairs(playerDataManagers) do
        dataManager:destroy()
    end
    
    playerDataManagers = {}
    playerSessions = {}
    
    print("PlayerManager: Cleaned up")
end

return PlayerManager
