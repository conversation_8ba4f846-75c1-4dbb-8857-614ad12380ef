-- StreamingSystem.lua
-- Core streaming mechanics and calculations

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

local StreamingSystem = {}
StreamingSystem.__index = StreamingSystem

-- Active streaming sessions
local activeStreams = {}
local streamUpdateConnection = nil

-- Stream session class
local StreamSession = {}
StreamSession.__index = StreamSession

function StreamSession.new(player, studioType, equipment)
    local self = setmetatable({}, StreamSession)
    
    self.player = player
    self.studioType = studioType
    self.equipment = equipment or {}
    self.startTime = tick()
    self.isActive = true
    
    -- Calculate base stats
    local studioConfig = GameConfig.StudioTypes[studioType]
    self.baseViewers = studioConfig and studioConfig.baseViewers or 5
    
    -- Calculate equipment multipliers
    self.equipmentMultipliers = {}
    for equipType, equipData in pairs(equipment) do
        local equipConfig = GameConfig.getEquipmentLevel(equipType, equipData.level or 1)
        if equipConfig then
            self.equipmentMultipliers[equipType] = equipConfig.multiplier
        end
    end
    
    -- Initialize stream stats
    self.currentViewers = self.baseViewers
    self.totalViews = 0
    self.totalLikes = 0
    self.totalFollowers = 0
    self.totalEarnings = 0
    
    return self
end

function StreamSession:update(deltaTime)
    if not self.isActive then return end
    
    local streamDuration = tick() - self.startTime
    
    -- Calculate current performance
    local performance = self:calculatePerformance(streamDuration)
    
    -- Update stats
    self.currentViewers = performance.viewers
    self.totalViews = self.totalViews + performance.viewers * deltaTime
    self.totalLikes = self.totalLikes + performance.likes
    self.totalFollowers = self.totalFollowers + performance.followers
    self.totalEarnings = self.totalEarnings + performance.cash
    
    -- Send update to client
    EventManager.Streaming.StreamEarningsUpdate:FireClient(self.player, {
        viewers = self.currentViewers,
        totalViews = math.floor(self.totalViews),
        totalLikes = self.totalLikes,
        totalFollowers = self.totalFollowers,
        totalEarnings = math.floor(self.totalEarnings),
        duration = streamDuration
    })
    
    return performance
end

function StreamSession:calculatePerformance(duration)
    -- Base calculation using GameConfig
    local studioMultiplier = 1
    local studioConfig = GameConfig.StudioTypes[self.studioType]
    if studioConfig then
        studioMultiplier = studioConfig.baseViewers / 5 -- Normalize to base
    end
    
    -- Calculate final performance
    return GameConfig.calculateStreamEarnings(
        self.baseViewers,
        self.equipmentMultipliers,
        studioMultiplier,
        duration
    )
end

function StreamSession:stop()
    self.isActive = false
    local duration = tick() - self.startTime
    
    return {
        duration = duration,
        totalViews = math.floor(self.totalViews),
        totalLikes = self.totalLikes,
        totalFollowers = self.totalFollowers,
        totalEarnings = math.floor(self.totalEarnings)
    }
end

-- StreamingSystem methods
function StreamingSystem.initialize()
    print("StreamingSystem: Initializing...")
    
    -- Connect event handlers
    EventManager.Streaming.StartStream.OnServerEvent:Connect(StreamingSystem.onStartStream)
    EventManager.Streaming.StopStream.OnServerEvent:Connect(StreamingSystem.onStopStream)
    EventManager.Streaming.GetStreamStatus.OnServerInvoke = StreamingSystem.getStreamStatus
    EventManager.Streaming.CanStartStream.OnServerInvoke = StreamingSystem.canStartStream
    
    -- Start update loop
    StreamingSystem.startUpdateLoop()
    
    print("StreamingSystem: Initialized successfully")
end

function StreamingSystem.onStartStream(player, data)
    -- Validate player can start streaming
    if not StreamingSystem.canStartStream(player) then
        EventManager.Streaming.StartStream:FireClient(player, {success = false, reason = "Cannot start stream"})
        return
    end
    
    -- Validate data
    if not EventManager.validateEventData("StartStream", data) then
        EventManager.Streaming.StartStream:FireClient(player, {success = false, reason = "Invalid data"})
        return
    end
    
    -- Check rate limit
    if not EventManager.checkRateLimit("StartStream", player) then
        local remaining = EventManager.getRemainingCooldown("StartStream", player)
        EventManager.Streaming.StartStream:FireClient(player, {
            success = false, 
            reason = "Rate limited",
            cooldown = remaining
        })
        return
    end
    
    -- Create new stream session
    local session = StreamSession.new(player, data.studioType, data.equipment)
    activeStreams[player.UserId] = session
    
    -- Log event
    EventManager.logEvent("StartStream", player, data)
    
    -- Notify client
    EventManager.Streaming.StartStream:FireClient(player, {success = true})
    
    print(player.Name, "started streaming in", data.studioType)
end

function StreamingSystem.onStopStream(player)
    local session = activeStreams[player.UserId]
    if not session then
        EventManager.Streaming.StopStream:FireClient(player, {success = false, reason = "No active stream"})
        return
    end
    
    -- Check rate limit
    if not EventManager.checkRateLimit("StopStream", player) then
        return
    end
    
    -- Stop session and get final stats
    local finalStats = session:stop()
    activeStreams[player.UserId] = nil
    
    -- Log event
    EventManager.logEvent("StopStream", player, finalStats)
    
    -- Notify client with final stats
    EventManager.Streaming.StopStream:FireClient(player, {
        success = true,
        finalStats = finalStats
    })
    
    print(player.Name, "stopped streaming. Duration:", math.floor(finalStats.duration), "seconds")
    
    return finalStats
end

function StreamingSystem.getStreamStatus(player)
    local session = activeStreams[player.UserId]
    if not session then
        return {isStreaming = false}
    end
    
    local duration = tick() - session.startTime
    return {
        isStreaming = true,
        duration = duration,
        currentViewers = session.currentViewers,
        totalViews = math.floor(session.totalViews),
        totalLikes = session.totalLikes,
        totalFollowers = session.totalFollowers,
        totalEarnings = math.floor(session.totalEarnings)
    }
end

function StreamingSystem.canStartStream(player)
    -- Check if player already has an active stream
    if activeStreams[player.UserId] then
        return false
    end
    
    -- Add additional checks here:
    -- - Player has required equipment
    -- - Player is in their studio
    -- - Player has valid plot assignment
    
    return true
end

function StreamingSystem.startUpdateLoop()
    if streamUpdateConnection then
        streamUpdateConnection:Disconnect()
    end
    
    local lastUpdate = tick()
    streamUpdateConnection = RunService.Heartbeat:Connect(function()
        local currentTime = tick()
        local deltaTime = currentTime - lastUpdate
        
        -- Update every StreamTickRate seconds
        if deltaTime >= GameConfig.Settings.StreamTickRate then
            StreamingSystem.updateAllStreams(deltaTime)
            lastUpdate = currentTime
        end
    end)
end

function StreamingSystem.updateAllStreams(deltaTime)
    for userId, session in pairs(activeStreams) do
        local player = Players:GetPlayerByUserId(userId)
        if player then
            session:update(deltaTime)
        else
            -- Player left, clean up session
            activeStreams[userId] = nil
        end
    end
end

function StreamingSystem.getActiveStreams()
    return activeStreams
end

function StreamingSystem.cleanup()
    if streamUpdateConnection then
        streamUpdateConnection:Disconnect()
        streamUpdateConnection = nil
    end
    
    activeStreams = {}
    print("StreamingSystem: Cleaned up")
end

return StreamingSystem
