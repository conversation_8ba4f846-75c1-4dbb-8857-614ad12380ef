-- TestScript.server.lua
-- Simple test script to verify game functionality

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Wait for shared modules to load
repeat wait() until ReplicatedStorage:FindFirstChild("Shared")

local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)

-- Test script
local function runTests()
    print("\n=== RUNNING GROWN A STREAMER TESTS ===")
    
    -- Test 1: GameConfig loading
    print("\n--- Test 1: GameConfig Loading ---")
    local success1 = pcall(function()
        print("Game Version:", GameConfig.Settings.Version)
        print("Total Plots:", GameConfig.Settings.TotalPlots)
        
        local studioCount = 0
        for studioType, config in pairs(GameConfig.StudioTypes) do
            studioCount = studioCount + 1
            print("Studio:", config.name, "- Price:", config.price)
        end
        print("Total Studios:", studioCount)
        
        local equipCount = 0
        for equipType, config in pairs(GameConfig.EquipmentTypes) do
            equipCount = equipCount + 1
            print("Equipment:", equipType, "- Category:", config.category)
        end
        print("Total Equipment Types:", equipCount)
    end)
    
    if success1 then
        print("✅ GameConfig test PASSED")
    else
        print("❌ GameConfig test FAILED")
    end
    
    -- Test 2: Event system
    print("\n--- Test 2: Event System ---")
    local success2 = pcall(function()
        local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)
        print("EventManager loaded successfully")
        print("Available categories:", table.concat({"Streaming", "Placement", "Shop", "Social", "Data"}, ", "))
    end)
    
    if success2 then
        print("✅ Event system test PASSED")
    else
        print("❌ Event system test FAILED")
    end
    
    -- Test 3: DataManager
    print("\n--- Test 3: DataManager ---")
    local success3 = pcall(function()
        local DataManager = require(ReplicatedStorage.Shared.Modules.DataManager)
        print("DataManager loaded successfully")
        print("DataManager module structure verified")
    end)
    
    if success3 then
        print("✅ DataManager test PASSED")
    else
        print("❌ DataManager test FAILED")
    end
    
    -- Test 4: Player connection simulation
    print("\n--- Test 4: Player Connection Simulation ---")
    local success4 = pcall(function()
        Players.PlayerAdded:Connect(function(player)
            print("Player joined:", player.Name)
            print("Player will be assigned a plot and initialized")
        end)
        
        Players.PlayerRemoving:Connect(function(player)
            print("Player leaving:", player.Name)
        end)
    end)
    
    if success4 then
        print("✅ Player connection test PASSED")
    else
        print("❌ Player connection test FAILED")
    end
    
    print("\n=== TEST SUMMARY ===")
    local totalTests = 4
    local passedTests = (success1 and 1 or 0) + (success2 and 1 or 0) + (success3 and 1 or 0) + (success4 and 1 or 0)
    
    print("Tests passed:", passedTests .. "/" .. totalTests)
    
    if passedTests == totalTests then
        print("🎉 ALL TESTS PASSED! Game is ready to run!")
    else
        print("⚠️ Some tests failed. Check the errors above.")
    end
    
    print("=== END OF TESTS ===\n")
end

-- Run tests after a short delay
wait(2)
runTests()

-- Cleanup test script after running
game:GetService("Debris"):AddItem(script, 10)
