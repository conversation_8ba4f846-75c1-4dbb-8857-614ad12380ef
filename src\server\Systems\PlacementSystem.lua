-- PlacementSystem.lua
-- Server-side equipment placement system with validation

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local Players = game:GetService("Players")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

local PlacementSystem = {}

-- Player placement data
local playerPlacements = {} -- [userId] = {items = {}, zones = {}}
local placementZones = {} -- [plotId] = {zones = {}}

-- Placement validation
local PLACEMENT_RAYCAST_PARAMS = RaycastParams.new()
PLACEMENT_RAYCAST_PARAMS.FilterType = Enum.RaycastFilterType.Blacklist

function PlacementSystem.initialize()
    print("PlacementSystem: Initializing...")
    
    -- Connect event handlers
    EventManager.Placement.StartPlacement.OnServerEvent:Connect(PlacementSystem.onStartPlacement)
    EventManager.Placement.ConfirmPlacement.OnServerEvent:Connect(PlacementSystem.onConfirmPlacement)
    EventManager.Placement.CancelPlacement.OnServerEvent:Connect(PlacementSystem.onCancelPlacement)
    EventManager.Placement.RotateItem.OnServerEvent:Connect(PlacementSystem.onRotateItem)
    EventManager.Placement.DeleteItem.OnServerEvent:Connect(PlacementSystem.onDeleteItem)
    EventManager.Placement.MoveItem.OnServerEvent:Connect(PlacementSystem.onMoveItem)
    
    -- Connect functions
    EventManager.Placement.ValidatePlacement.OnServerInvoke = PlacementSystem.validatePlacement
    EventManager.Placement.GetPlacementZones.OnServerInvoke = PlacementSystem.getPlacementZones
    EventManager.Placement.GetPlayerItems.OnServerInvoke = PlacementSystem.getPlayerItems
    
    -- Initialize placement zones for all plots
    PlacementSystem.initializePlacementZones()
    
    print("PlacementSystem: Initialized successfully")
end

function PlacementSystem.initializePlacementZones()
    -- Create placement zones for each plot
    for plotId = 1, GameConfig.Settings.TotalPlots do
        PlacementSystem.createPlacementZonesForPlot(plotId)
    end
end

function PlacementSystem.createPlacementZonesForPlot(plotId)
    local plotFolder = Workspace:FindFirstChild("Plot" .. plotId)
    if not plotFolder then
        -- Create plot folder if it doesn't exist
        plotFolder = Instance.new("Folder")
        plotFolder.Name = "Plot" .. plotId
        plotFolder.Parent = Workspace
    end
    
    -- Create placement zones folder
    local zonesFolder = plotFolder:FindFirstChild("PlacementZones")
    if not zonesFolder then
        zonesFolder = Instance.new("Folder")
        zonesFolder.Name = "PlacementZones"
        zonesFolder.Parent = plotFolder
    end
    
    -- Create default placement zones (black parts)
    local zones = {}
    local zonePositions = {
        Vector3.new(0, 0.5, 0),    -- Center
        Vector3.new(-5, 0.5, 0),   -- Left
        Vector3.new(5, 0.5, 0),    -- Right
        Vector3.new(0, 0.5, -5),   -- Front
        Vector3.new(0, 0.5, 5),    -- Back
    }
    
    for i, position in ipairs(zonePositions) do
        local zone = PlacementSystem.createPlacementZone(i, position)
        zone.Parent = zonesFolder
        table.insert(zones, {
            id = i,
            part = zone,
            position = position,
            occupied = false,
            item = nil
        })
    end
    
    placementZones[plotId] = {zones = zones}
end

function PlacementSystem.createPlacementZone(id, position)
    local zone = Instance.new("Part")
    zone.Name = "PlacementZone" .. id
    zone.Size = Vector3.new(4, 0.2, 4)
    zone.Position = position
    zone.Material = Enum.Material.Neon
    zone.BrickColor = BrickColor.new("Really black")
    zone.Anchored = true
    zone.CanCollide = false
    zone.TopSurface = Enum.SurfaceType.Smooth
    zone.BottomSurface = Enum.SurfaceType.Smooth
    
    -- Add ProximityPrompt
    local prompt = Instance.new("ProximityPrompt")
    prompt.ActionText = "Place Item"
    prompt.ObjectText = "Placement Zone"
    prompt.HoldDuration = 0.5
    prompt.MaxActivationDistance = 10
    prompt.Parent = zone
    
    -- Connect prompt event
    prompt.Triggered:Connect(function(player)
        PlacementSystem.onZoneTriggered(player, id, zone)
    end)
    
    return zone
end

function PlacementSystem.onZoneTriggered(player, zoneId, zonePart)
    -- Check if player has items to place
    local playerItems = PlacementSystem.getPlayerItems(player)
    if not playerItems or #playerItems == 0 then
        -- Send message to player
        EventManager.Placement.PlacementUpdate:FireClient(player, {
            type = "error",
            message = "No items available to place"
        })
        return
    end
    
    -- Start placement mode for this zone
    EventManager.Placement.StartPlacement:FireClient(player, {
        zoneId = zoneId,
        zonePosition = zonePart.Position,
        availableItems = playerItems
    })
end

function PlacementSystem.onStartPlacement(player, data)
    -- Validate request
    if not data or not data.itemType or not data.zoneId then
        return
    end
    
    -- Check rate limit
    if not EventManager.checkRateLimit("StartPlacement", player) then
        return
    end
    
    -- Validate player owns the item
    if not PlacementSystem.playerOwnsItem(player, data.itemType, data.level) then
        EventManager.Placement.PlacementUpdate:FireClient(player, {
            type = "error",
            message = "You don't own this item"
        })
        return
    end
    
    -- Log event
    EventManager.logEvent("StartPlacement", player, data)
    
    print(player.Name, "started placing", data.itemType, "level", data.level)
end

function PlacementSystem.onConfirmPlacement(player, data)
    -- Validate placement data
    if not PlacementSystem.validatePlacementData(data) then
        EventManager.Placement.PlacementUpdate:FireClient(player, {
            type = "error",
            message = "Invalid placement data"
        })
        return
    end
    
    -- Check rate limit
    if not EventManager.checkRateLimit("ConfirmPlacement", player) then
        return
    end
    
    -- Validate placement position
    if not PlacementSystem.validatePlacement(player, data) then
        EventManager.Placement.PlacementUpdate:FireClient(player, {
            type = "error",
            message = "Invalid placement position"
        })
        return
    end
    
    -- Create the item in the world
    local item = PlacementSystem.createPlacedItem(player, data)
    if item then
        -- Store placement data
        PlacementSystem.storePlacement(player, data, item)
        
        -- Notify client of success
        EventManager.Placement.PlacementUpdate:FireClient(player, {
            type = "success",
            message = "Item placed successfully",
            itemId = item:GetAttribute("ItemId")
        })
        
        -- Update placement zone
        PlacementSystem.updatePlacementZone(data.zoneId, true, item)
        
        -- Log event
        EventManager.logEvent("ConfirmPlacement", player, data)
        
        print(player.Name, "placed", data.itemType, "at", data.position)
    else
        EventManager.Placement.PlacementUpdate:FireClient(player, {
            type = "error",
            message = "Failed to create item"
        })
    end
end

function PlacementSystem.onCancelPlacement(player, data)
    -- Simply notify client that placement was cancelled
    EventManager.Placement.PlacementUpdate:FireClient(player, {
        type = "cancelled",
        message = "Placement cancelled"
    })
    
    print(player.Name, "cancelled placement")
end

function PlacementSystem.onRotateItem(player, data)
    -- Check rate limit
    if not EventManager.checkRateLimit("RotateItem", player) then
        return
    end
    
    -- Validate rotation data
    if not data or not data.rotation then
        return
    end
    
    -- Send rotation update to client
    EventManager.Placement.PlacementUpdate:FireClient(player, {
        type = "rotation",
        rotation = data.rotation
    })
end

function PlacementSystem.onDeleteItem(player, data)
    -- Check rate limit
    if not EventManager.checkRateLimit("DeleteItem", player) then
        return
    end
    
    -- Validate item ownership
    if not PlacementSystem.playerOwnsPlacedItem(player, data.itemId) then
        EventManager.Placement.PlacementUpdate:FireClient(player, {
            type = "error",
            message = "You don't own this item"
        })
        return
    end
    
    -- Remove item from world
    local success = PlacementSystem.removePlacedItem(player, data.itemId)
    if success then
        EventManager.Placement.PlacementUpdate:FireClient(player, {
            type = "success",
            message = "Item removed successfully"
        })
        
        -- Log event
        EventManager.logEvent("DeleteItem", player, data)
        
        print(player.Name, "deleted item", data.itemId)
    else
        EventManager.Placement.PlacementUpdate:FireClient(player, {
            type = "error",
            message = "Failed to remove item"
        })
    end
end

function PlacementSystem.onMoveItem(player, data)
    -- TODO: Implement item moving functionality
    print(player.Name, "requested to move item", data.itemId)
end

function PlacementSystem.validatePlacement(player, data)
    -- Check if position is within valid placement zone
    local plotId = PlacementSystem.getPlayerPlotId(player)
    if not plotId then return false end
    
    local zones = placementZones[plotId]
    if not zones then return false end
    
    -- Find the target zone
    local targetZone = nil
    for _, zone in ipairs(zones.zones) do
        if zone.id == data.zoneId then
            targetZone = zone
            break
        end
    end
    
    if not targetZone then return false end
    
    -- Check if zone is occupied
    if targetZone.occupied then return false end
    
    -- Check distance from zone center
    local distance = (data.position - targetZone.position).Magnitude
    if distance > GameConfig.Settings.PlacementValidationRadius then
        return false
    end
    
    -- Additional validation can be added here
    return true
end

function PlacementSystem.getPlacementZones(player)
    local plotId = PlacementSystem.getPlayerPlotId(player)
    if not plotId then return {} end
    
    local zones = placementZones[plotId]
    if not zones then return {} end
    
    local zoneData = {}
    for _, zone in ipairs(zones.zones) do
        table.insert(zoneData, {
            id = zone.id,
            position = zone.position,
            occupied = zone.occupied,
            itemType = zone.item and zone.item.itemType or nil
        })
    end
    
    return zoneData
end

function PlacementSystem.getPlayerItems(player)
    -- TODO: Get from player data
    -- For now, return sample items
    return {
        {itemType = "PC", level = 1, owned = true},
        {itemType = "MICROPHONE", level = 1, owned = true},
        {itemType = "CAMERA", level = 1, owned = true},
        {itemType = "CHAIR", level = 1, owned = true},
        {itemType = "LIGHTING", level = 1, owned = true},
        {itemType = "DESK", level = 1, owned = true}
    }
end

-- Helper functions
function PlacementSystem.validatePlacementData(data)
    return data and data.itemType and data.position and data.rotation and data.zoneId
end

function PlacementSystem.playerOwnsItem(player, itemType, level)
    -- TODO: Check player inventory
    return true -- Placeholder
end

function PlacementSystem.playerOwnsPlacedItem(player, itemId)
    -- TODO: Check if player owns the placed item
    return true -- Placeholder
end

function PlacementSystem.getPlayerPlotId(player)
    -- TODO: Get from plot management system
    return 1 -- Placeholder
end

function PlacementSystem.createPlacedItem(player, data)
    -- TODO: Create actual 3D model based on item type and level
    local item = Instance.new("Part")
    item.Name = data.itemType .. "_" .. data.level
    item.Size = Vector3.new(2, 2, 2)
    item.Position = data.position
    item.Rotation = data.rotation
    item.Anchored = true
    item.BrickColor = BrickColor.random()
    
    -- Add item metadata
    item:SetAttribute("ItemId", game:GetService("HttpService"):GenerateGUID(false))
    item:SetAttribute("ItemType", data.itemType)
    item:SetAttribute("Level", data.level)
    item:SetAttribute("Owner", player.UserId)
    
    -- Parent to appropriate location
    local plotId = PlacementSystem.getPlayerPlotId(player)
    local plotFolder = Workspace:FindFirstChild("Plot" .. plotId)
    if plotFolder then
        item.Parent = plotFolder
    else
        item.Parent = Workspace
    end
    
    return item
end

function PlacementSystem.storePlacement(player, data, item)
    local userId = player.UserId
    if not playerPlacements[userId] then
        playerPlacements[userId] = {items = {}}
    end
    
    table.insert(playerPlacements[userId].items, {
        itemId = item:GetAttribute("ItemId"),
        itemType = data.itemType,
        level = data.level,
        position = data.position,
        rotation = data.rotation,
        zoneId = data.zoneId,
        item = item
    })
end



function PlacementSystem.removePlacedItem(player, itemId)
    local userId = player.UserId
    local playerData = playerPlacements[userId]
    if not playerData then return false end

    -- Find and remove item
    for i, itemData in ipairs(playerData.items) do
        if itemData.itemId == itemId then
            -- Remove from world
            if itemData.item and itemData.item.Parent then
                itemData.item:Destroy()
            end

            -- Update placement zone
            PlacementSystem.updatePlacementZone(itemData.zoneId, false, nil)

            -- Remove from player data
            table.remove(playerData.items, i)
            return true
        end
    end

    return false
end

function PlacementSystem.updatePlacementZone(zoneId, occupied, item)
    -- Update zone status across all plots (find the right one)
    for plotId, plotData in pairs(placementZones) do
        for _, zone in ipairs(plotData.zones) do
            if zone.id == zoneId then
                zone.occupied = occupied
                zone.item = item and {
                    itemType = item:GetAttribute("ItemType"),
                    level = item:GetAttribute("Level"),
                    itemId = item:GetAttribute("ItemId")
                } or nil

                -- Update visual state of zone
                if zone.part then
                    zone.part.BrickColor = occupied and BrickColor.new("Dark green") or BrickColor.new("Really black")
                    local prompt = zone.part:FindFirstChild("ProximityPrompt")
                    if prompt then
                        prompt.ActionText = occupied and "Remove Item" or "Place Item"
                        prompt.Enabled = not occupied
                    end
                end
                return
            end
        end
    end
end

function PlacementSystem.getPlayerPlacedItems(player)
    local userId = player.UserId
    local playerData = playerPlacements[userId]
    if not playerData then return {} end

    local items = {}
    for _, itemData in ipairs(playerData.items) do
        table.insert(items, {
            itemId = itemData.itemId,
            itemType = itemData.itemType,
            level = itemData.level,
            position = itemData.position,
            rotation = itemData.rotation,
            zoneId = itemData.zoneId
        })
    end

    return items
end

function PlacementSystem.cleanup()
    playerPlacements = {}
    placementZones = {}
    print("PlacementSystem: Cleaned up")
end

return PlacementSystem
