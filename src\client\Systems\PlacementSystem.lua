-- PlacementSystem.lua (Client)
-- Client-side equipment placement with visual feedback

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

local PlacementSystem = {}
local player = Players.LocalPlayer

-- Placement state
local isPlacing = false
local currentItem = nil
local placementPreview = nil
local currentZone = nil
local rotationAngle = 0
local placementConnection = nil

-- UI elements
local placementUI = nil
local itemSelector = nil
local rotationControls = nil

function PlacementSystem.initialize()
    print("PlacementSystem (Client): Initializing...")
    
    -- Connect to server events
    EventManager.Placement.StartPlacement.OnClientEvent:Connect(PlacementSystem.onStartPlacement)
    EventManager.Placement.PlacementUpdate.OnClientEvent:Connect(PlacementSystem.onPlacementUpdate)
    
    -- Create placement UI
    PlacementSystem.createPlacementUI()
    
    -- Setup input handling
    PlacementSystem.setupInputHandling()
    
    print("PlacementSystem (Client): Initialized successfully")
end

function PlacementSystem.createPlacementUI()
    -- Create main placement UI
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "PlacementUI"
    screenGui.Parent = player.PlayerGui
    
    -- Item selector frame
    itemSelector = Instance.new("Frame")
    itemSelector.Name = "ItemSelector"
    itemSelector.Size = UDim2.new(0, 400, 0, 300)
    itemSelector.Position = UDim2.new(0.5, -200, 0.5, -150)
    itemSelector.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    itemSelector.BackgroundTransparency = 0.1
    itemSelector.Visible = false
    itemSelector.Parent = screenGui
    
    local selectorCorner = Instance.new("UICorner")
    selectorCorner.CornerRadius = UDim.new(0, 12)
    selectorCorner.Parent = itemSelector
    
    -- Title
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Size = UDim2.new(1, 0, 0, 40)
    title.BackgroundTransparency = 1
    title.Text = "Select Item to Place"
    title.TextColor3 = Color3.new(1, 1, 1)
    title.TextScaled = true
    title.Font = Enum.Font.GothamBold
    title.Parent = itemSelector
    
    -- Items scroll frame
    local itemsFrame = Instance.new("ScrollingFrame")
    itemsFrame.Name = "ItemsFrame"
    itemsFrame.Size = UDim2.new(1, -20, 1, -80)
    itemsFrame.Position = UDim2.new(0, 10, 0, 50)
    itemsFrame.BackgroundTransparency = 1
    itemsFrame.ScrollBarThickness = 8
    itemsFrame.Parent = itemSelector
    
    local itemsLayout = Instance.new("UIListLayout")
    itemsLayout.Padding = UDim.new(0, 5)
    itemsLayout.Parent = itemsFrame
    
    -- Close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 80, 0, 30)
    closeButton.Position = UDim2.new(1, -90, 1, -40)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "Cancel"
    closeButton.TextColor3 = Color3.new(1, 1, 1)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.Gotham
    closeButton.Parent = itemSelector
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 6)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        PlacementSystem.cancelPlacement()
    end)
    
    -- Rotation controls
    rotationControls = Instance.new("Frame")
    rotationControls.Name = "RotationControls"
    rotationControls.Size = UDim2.new(0, 200, 0, 60)
    rotationControls.Position = UDim2.new(0, 20, 1, -80)
    rotationControls.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    rotationControls.BackgroundTransparency = 0.2
    rotationControls.Visible = false
    rotationControls.Parent = screenGui
    
    local controlsCorner = Instance.new("UICorner")
    controlsCorner.CornerRadius = UDim.new(0, 8)
    controlsCorner.Parent = rotationControls
    
    -- Rotation buttons
    local rotateLeftButton = Instance.new("TextButton")
    rotateLeftButton.Name = "RotateLeft"
    rotateLeftButton.Size = UDim2.new(0, 50, 0, 40)
    rotateLeftButton.Position = UDim2.new(0, 10, 0, 10)
    rotateLeftButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
    rotateLeftButton.Text = "◀"
    rotateLeftButton.TextColor3 = Color3.new(1,1,1)
    rotateLeftButton.TextScaled = true
    rotateLeftButton.Font = Enum.Font.GothamBold
    rotateLeftButton.Parent = rotationControls
    
    local rotateRightButton = Instance.new("TextButton")
    rotateRightButton.Name = "RotateRight"
    rotateRightButton.Size = UDim2.new(0, 50, 0, 40)
    rotateRightButton.Position = UDim2.new(1, -60, 0, 10)
    rotateRightButton.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
    rotateRightButton.Text = "▶"
    rotateRightButton.TextColor3 = Color3.new(1,1,1)
    rotateRightButton.TextScaled = true
    rotateRightButton.Font = Enum.Font.GothamBold
    rotateRightButton.Parent = rotationControls
    
    -- Confirm button
    local confirmButton = Instance.new("TextButton")
    confirmButton.Name = "ConfirmButton"
    confirmButton.Size = UDim2.new(0, 70, 0, 40)
    confirmButton.Position = UDim2.new(0.5, -35, 0, 10)
    confirmButton.BackgroundColor3 = Color3.fromRGB(85, 170, 85)
    confirmButton.Text = "Place"
    confirmButton.TextColor3 = Color3.new(1,1,1)
    confirmButton.TextScaled = true
    confirmButton.Font = Enum.Font.GothamBold
    confirmButton.Parent = rotationControls
    
    -- Connect rotation events
    rotateLeftButton.MouseButton1Click:Connect(function()
        PlacementSystem.rotateItem(-GameConfig.Settings.PlacementRotationStep)
    end)
    
    rotateRightButton.MouseButton1Click:Connect(function()
        PlacementSystem.rotateItem(GameConfig.Settings.PlacementRotationStep)
    end)
    
    confirmButton.MouseButton1Click:Connect(function()
        PlacementSystem.confirmPlacement()
    end)
    
    placementUI = screenGui
end

function PlacementSystem.setupInputHandling()
    -- Handle keyboard input for rotation
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed or not isPlacing then return end
        
        if input.KeyCode == Enum.KeyCode.Q then
            PlacementSystem.rotateItem(-GameConfig.Settings.PlacementRotationStep)
        elseif input.KeyCode == Enum.KeyCode.E then
            PlacementSystem.rotateItem(GameConfig.Settings.PlacementRotationStep)
        elseif input.KeyCode == Enum.KeyCode.Return then
            PlacementSystem.confirmPlacement()
        elseif input.KeyCode == Enum.KeyCode.Escape then
            PlacementSystem.cancelPlacement()
        end
    end)
end

function PlacementSystem.onStartPlacement(data)
    if not data or not data.availableItems then return end
    
    currentZone = data
    PlacementSystem.showItemSelector(data.availableItems)
end

function PlacementSystem.showItemSelector(items)
    if not itemSelector then return end
    
    -- Clear existing items
    local itemsFrame = itemSelector:FindFirstChild("ItemsFrame")
    if itemsFrame then
        for _, child in ipairs(itemsFrame:GetChildren()) do
            if child:IsA("TextButton") then
                child:Destroy()
            end
        end
    end
    
    -- Create item buttons
    for _, item in ipairs(items) do
        local itemButton = PlacementSystem.createItemButton(item)
        itemButton.Parent = itemsFrame
    end
    
    -- Update canvas size
    local layout = itemsFrame:FindFirstChild("UIListLayout")
    if layout then
        itemsFrame.CanvasSize = UDim2.new(0, 0, 0, layout.AbsoluteContentSize.Y)
    end
    
    -- Show selector
    itemSelector.Visible = true
end

function PlacementSystem.createItemButton(item)
    local button = Instance.new("TextButton")
    button.Name = item.itemType .. "Button"
    button.Size = UDim2.new(1, -10, 0, 50)
    button.BackgroundColor3 = Color3.fromRGB(80, 80, 80)
    button.Text = item.itemType .. " (Level " .. item.level .. ")"
    button.TextColor3 = Color3.new(1,1,1)
    button.TextScaled = true
    button.Font = Enum.Font.Gotham
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 6)
    corner.Parent = button
    
    button.MouseButton1Click:Connect(function()
        PlacementSystem.selectItem(item)
    end)
    
    return button
end

function PlacementSystem.selectItem(item)
    currentItem = item
    itemSelector.Visible = false
    
    -- Start placement mode
    PlacementSystem.startPlacementMode()
end

function PlacementSystem.startPlacementMode()
    if not currentItem or not currentZone then return end
    
    isPlacing = true
    rotationAngle = 0
    
    -- Create placement preview
    PlacementSystem.createPlacementPreview()
    
    -- Show rotation controls
    rotationControls.Visible = true
    
    -- Start placement update loop
    placementConnection = RunService.Heartbeat:Connect(PlacementSystem.updatePlacement)
    
    print("Started placing", currentItem.itemType)
end

function PlacementSystem.createPlacementPreview()
    if placementPreview then
        placementPreview:Destroy()
    end
    
    -- Create preview model (simplified version)
    placementPreview = Instance.new("Part")
    placementPreview.Name = "PlacementPreview"
    placementPreview.Size = Vector3.new(2, 2, 2) -- TODO: Get actual size from item config
    placementPreview.Material = Enum.Material.ForceField
    placementPreview.BrickColor = BrickColor.new("Bright green")
    placementPreview.CanCollide = false
    placementPreview.Anchored = true
    placementPreview.Parent = workspace
    
    -- Add transparency animation
    local tween = TweenService:Create(
        placementPreview,
        TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
        {Transparency = 0.7}
    )
    tween:Play()
end

function PlacementSystem.updatePlacement()
    if not placementPreview or not currentZone then return end
    
    -- Update preview position and rotation
    placementPreview.Position = currentZone.zonePosition + Vector3.new(0, 1, 0)
    placementPreview.Rotation = Vector3.new(0, rotationAngle, 0)
end

function PlacementSystem.rotateItem(angle)
    if not isPlacing then return end
    
    rotationAngle = rotationAngle + angle
    
    -- Send rotation to server
    EventManager.Placement.RotateItem:FireServer({
        rotation = Vector3.new(0, rotationAngle, 0)
    })
end

function PlacementSystem.confirmPlacement()
    if not isPlacing or not currentItem or not currentZone or not placementPreview then return end
    
    -- Send placement confirmation to server
    EventManager.Placement.ConfirmPlacement:FireServer({
        itemType = currentItem.itemType,
        level = currentItem.level,
        position = placementPreview.Position,
        rotation = placementPreview.Rotation,
        zoneId = currentZone.zoneId
    })
    
    -- End placement mode
    PlacementSystem.endPlacementMode()
end

function PlacementSystem.cancelPlacement()
    -- Send cancellation to server
    EventManager.Placement.CancelPlacement:FireServer()
    
    -- End placement mode
    PlacementSystem.endPlacementMode()
end

function PlacementSystem.endPlacementMode()
    isPlacing = false
    currentItem = nil
    currentZone = nil
    rotationAngle = 0
    
    -- Hide UI
    if itemSelector then itemSelector.Visible = false end
    if rotationControls then rotationControls.Visible = false end
    
    -- Clean up preview
    if placementPreview then
        placementPreview:Destroy()
        placementPreview = nil
    end
    
    -- Disconnect update loop
    if placementConnection then
        placementConnection:Disconnect()
        placementConnection = nil
    end
end

function PlacementSystem.onPlacementUpdate(data)
    if data.type == "success" then
        print("Placement successful:", data.message)
    elseif data.type == "error" then
        print("Placement error:", data.message)
    elseif data.type == "cancelled" then
        print("Placement cancelled")
    end
end

function PlacementSystem.cleanup()
    PlacementSystem.endPlacementMode()
    
    if placementUI then
        placementUI:Destroy()
        placementUI = nil
    end
    
    print("PlacementSystem (Client): Cleaned up")
end

return PlacementSystem
