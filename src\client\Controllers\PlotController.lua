-- PlotController.lua (Client)
-- Client-side plot management and UI

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

local PlotController = {}
local player = Players.LocalPlayer

-- Plot state
local currentPlot = nil
local plotUI = nil
local plotList = {}

function PlotController.initialize()
    print("PlotController: Initializing...")
    
    -- Connect to server events
    EventManager.Data.PlotAssigned.OnClientEvent:Connect(PlotController.onPlotAssigned)
    EventManager.Data.PlotLiberated.OnClientEvent:Connect(PlotController.onPlotLiberated)
    
    -- Create plot UI
    PlotController.createPlotUI()
    
    -- Request current plot info
    PlotController.requestPlotInfo()
    
    print("PlotController: Initialized successfully")
end

function PlotController.createPlotUI()
    -- Create main plot UI
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "PlotUI"
    screenGui.Parent = player.PlayerGui
    
    -- Plot info panel
    local plotInfo = Instance.new("Frame")
    plotInfo.Name = "PlotInfo"
    plotInfo.Size = UDim2.new(0, 250, 0, 120)
    plotInfo.Position = UDim2.new(1, -270, 0, 20)
    plotInfo.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    plotInfo.BackgroundTransparency = 0.1
    plotInfo.Visible = false
    plotInfo.Parent = screenGui
    
    local infoCorner = Instance.new("UICorner")
    infoCorner.CornerRadius = UDim.new(0, 12)
    infoCorner.Parent = plotInfo
    
    -- Plot info layout
    local infoLayout = Instance.new("UIListLayout")
    infoLayout.Padding = UDim.new(0, 5)
    infoLayout.Parent = plotInfo
    
    -- Title
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Size = UDim2.new(1, -10, 0, 30)
    title.BackgroundTransparency = 1
    title.Text = "Your Plot"
    title.TextColor3 = Color3.new(1,1,1)
    title.TextScaled = true
    title.Font = Enum.Font.GothamBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = plotInfo
    
    -- Plot ID label
    local plotIdLabel = Instance.new("TextLabel")
    plotIdLabel.Name = "PlotIdLabel"
    plotIdLabel.Size = UDim2.new(1, -10, 0, 20)
    plotIdLabel.BackgroundTransparency = 1
    plotIdLabel.Text = "Plot: Not Assigned"
    plotIdLabel.TextColor3 = Color3.new(1,1,1)
    plotIdLabel.TextScaled = true
    plotIdLabel.Font = Enum.Font.Gotham
    plotIdLabel.TextXAlignment = Enum.TextXAlignment.Left
    plotIdLabel.Parent = plotInfo
    
    -- Teleport button
    local teleportButton = Instance.new("TextButton")
    teleportButton.Name = "TeleportButton"
    teleportButton.Size = UDim2.new(1, -10, 0, 35)
    teleportButton.BackgroundColor3 = Color3.fromRGB(85, 170, 85)
    teleportButton.Text = "Teleport to Plot"
    teleportButton.TextColor3 = Color3.new(1,1,1)
    teleportButton.TextScaled = true
    teleportButton.Font = Enum.Font.GothamBold
    teleportButton.Parent = plotInfo
    
    local teleportCorner = Instance.new("UICorner")
    teleportCorner.CornerRadius = UDim.new(0, 6)
    teleportCorner.Parent = teleportButton
    
    teleportButton.MouseButton1Click:Connect(function()
        PlotController.teleportToPlot()
    end)
    
    -- Plot browser button
    local browserButton = Instance.new("TextButton")
    browserButton.Name = "BrowserButton"
    browserButton.Size = UDim2.new(1, -10, 0, 25)
    browserButton.BackgroundColor3 = Color3.fromRGB(100, 150, 200)
    browserButton.Text = "Browse Plots"
    browserButton.TextColor3 = Color3.new(1,1,1)
    browserButton.TextScaled = true
    browserButton.Font = Enum.Font.Gotham
    browserButton.Parent = plotInfo
    
    local browserCorner = Instance.new("UICorner")
    browserCorner.CornerRadius = UDim.new(0, 6)
    browserCorner.Parent = browserButton
    
    browserButton.MouseButton1Click:Connect(function()
        PlotController.openPlotBrowser()
    end)
    
    -- Plot browser window
    local plotBrowser = Instance.new("Frame")
    plotBrowser.Name = "PlotBrowser"
    plotBrowser.Size = UDim2.new(0, 500, 0, 400)
    plotBrowser.Position = UDim2.new(0.5, -250, 0.5, -200)
    plotBrowser.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    plotBrowser.BackgroundTransparency = 0.1
    plotBrowser.Visible = false
    plotBrowser.Parent = screenGui
    
    local browserWindowCorner = Instance.new("UICorner")
    browserWindowCorner.CornerRadius = UDim.new(0, 12)
    browserWindowCorner.Parent = plotBrowser
    
    -- Browser title
    local browserTitle = Instance.new("TextLabel")
    browserTitle.Name = "BrowserTitle"
    browserTitle.Size = UDim2.new(1, 0, 0, 40)
    browserTitle.BackgroundTransparency = 1
    browserTitle.Text = "Plot Browser"
    browserTitle.TextColor3 = Color3.new(1,1,1)
    browserTitle.TextScaled = true
    browserTitle.Font = Enum.Font.GothamBold
    browserTitle.Parent = plotBrowser
    
    -- Close browser button
    local closeBrowserButton = Instance.new("TextButton")
    closeBrowserButton.Name = "CloseBrowserButton"
    closeBrowserButton.Size = UDim2.new(0, 30, 0, 30)
    closeBrowserButton.Position = UDim2.new(1, -35, 0, 5)
    closeBrowserButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeBrowserButton.Text = "X"
    closeBrowserButton.TextColor3 = Color3.new(1,1,1)
    closeBrowserButton.TextScaled = true
    closeBrowserButton.Font = Enum.Font.GothamBold
    closeBrowserButton.Parent = plotBrowser
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 6)
    closeCorner.Parent = closeBrowserButton
    
    closeBrowserButton.MouseButton1Click:Connect(function()
        plotBrowser.Visible = false
    end)
    
    -- Plot list scroll frame
    local plotListFrame = Instance.new("ScrollingFrame")
    plotListFrame.Name = "PlotListFrame"
    plotListFrame.Size = UDim2.new(1, -20, 1, -60)
    plotListFrame.Position = UDim2.new(0, 10, 0, 50)
    plotListFrame.BackgroundTransparency = 1
    plotListFrame.ScrollBarThickness = 8
    plotListFrame.Parent = plotBrowser
    
    local plotListLayout = Instance.new("UIListLayout")
    plotListLayout.Padding = UDim.new(0, 5)
    plotListLayout.Parent = plotListFrame
    
    plotUI = screenGui
end

function PlotController.requestPlotInfo()
    -- Request current plot from server
    local plotData = EventManager.Data.GetPlayerPlot:InvokeServer()
    if plotData then
        PlotController.updatePlotInfo(plotData)
    end
end

function PlotController.onPlotAssigned(data)
    currentPlot = data
    PlotController.updatePlotInfo(data)
    PlotController.showPlotAssignedNotification(data)
end

function PlotController.onPlotLiberated(data)
    -- Update plot list if browser is open
    if plotUI and plotUI.PlotBrowser.Visible then
        PlotController.refreshPlotBrowser()
    end
end

function PlotController.updatePlotInfo(plotData)
    if not plotUI then return end
    
    local plotInfo = plotUI:FindFirstChild("PlotInfo")
    if not plotInfo then return end
    
    local plotIdLabel = plotInfo:FindFirstChild("PlotIdLabel")
    if plotIdLabel then
        plotIdLabel.Text = "Plot: " .. plotData.plotId
    end
    
    -- Show plot info panel
    plotInfo.Visible = true
    
    -- Animate panel appearance
    plotInfo.Position = UDim2.new(1, 0, 0, 20)
    local tween = TweenService:Create(
        plotInfo,
        TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
        {Position = UDim2.new(1, -270, 0, 20)}
    )
    tween:Play()
end

function PlotController.teleportToPlot()
    if not currentPlot then return end
    
    EventManager.Data.TeleportToPlot:FireServer({
        plotId = currentPlot.plotId
    })
end

function PlotController.openPlotBrowser()
    if not plotUI then return end
    
    local plotBrowser = plotUI:FindFirstChild("PlotBrowser")
    if not plotBrowser then return end
    
    -- Show browser
    plotBrowser.Visible = true
    
    -- Refresh plot list
    PlotController.refreshPlotBrowser()
    
    -- Animate browser appearance
    plotBrowser.Size = UDim2.new(0, 0, 0, 0)
    plotBrowser.Position = UDim2.new(0.5, 0, 0.5, 0)
    
    local tween = TweenService:Create(
        plotBrowser,
        TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
        {
            Size = UDim2.new(0, 500, 0, 400),
            Position = UDim2.new(0.5, -250, 0.5, -200)
        }
    )
    tween:Play()
end

function PlotController.refreshPlotBrowser()
    if not plotUI then return end
    
    local plotBrowser = plotUI:FindFirstChild("PlotBrowser")
    if not plotBrowser then return end
    
    local plotListFrame = plotBrowser:FindFirstChild("PlotListFrame")
    if not plotListFrame then return end
    
    -- Clear existing plot entries
    for _, child in ipairs(plotListFrame:GetChildren()) do
        if child:IsA("Frame") then
            child:Destroy()
        end
    end
    
    -- Get available plots from server
    local availablePlots = EventManager.Data.GetAvailablePlots:InvokeServer()
    
    -- Create plot entries
    for _, plotData in ipairs(availablePlots) do
        PlotController.createPlotEntry(plotListFrame, plotData)
    end
    
    -- Update canvas size
    local layout = plotListFrame:FindFirstChild("UIListLayout")
    if layout then
        plotListFrame.CanvasSize = UDim2.new(0, 0, 0, layout.AbsoluteContentSize.Y)
    end
end

function PlotController.createPlotEntry(parent, plotData)
    local entry = Instance.new("Frame")
    entry.Name = "PlotEntry" .. plotData.plotId
    entry.Size = UDim2.new(1, -10, 0, 60)
    entry.BackgroundColor3 = Color3.fromRGB(70, 70, 70)
    entry.Parent = parent
    
    local entryCorner = Instance.new("UICorner")
    entryCorner.CornerRadius = UDim.new(0, 8)
    entryCorner.Parent = entry
    
    -- Plot info
    local plotLabel = Instance.new("TextLabel")
    plotLabel.Name = "PlotLabel"
    plotLabel.Size = UDim2.new(0.7, 0, 1, 0)
    plotLabel.BackgroundTransparency = 1
    plotLabel.Text = "Plot " .. plotData.plotId .. "\nAvailable"
    plotLabel.TextColor3 = Color3.new(1,1,1)
    plotLabel.TextScaled = true
    plotLabel.Font = Enum.Font.Gotham
    plotLabel.TextXAlignment = Enum.TextXAlignment.Left
    plotLabel.Parent = entry
    
    -- Visit button
    local visitButton = Instance.new("TextButton")
    visitButton.Name = "VisitButton"
    visitButton.Size = UDim2.new(0.25, -5, 0, 40)
    visitButton.Position = UDim2.new(0.75, 0, 0.5, -20)
    visitButton.BackgroundColor3 = Color3.fromRGB(100, 150, 200)
    visitButton.Text = "Visit"
    visitButton.TextColor3 = Color3.new(1,1,1)
    visitButton.TextScaled = true
    visitButton.Font = Enum.Font.GothamBold
    visitButton.Parent = entry
    
    local visitCorner = Instance.new("UICorner")
    visitCorner.CornerRadius = UDim.new(0, 6)
    visitCorner.Parent = visitButton
    
    visitButton.MouseButton1Click:Connect(function()
        PlotController.visitPlot(plotData.plotId)
    end)
end

function PlotController.visitPlot(plotId)
    -- TODO: Implement plot visiting functionality
    print("Visiting plot", plotId)
end

function PlotController.showPlotAssignedNotification(plotData)
    -- Create temporary notification
    local notification = Instance.new("Frame")
    notification.Name = "PlotNotification"
    notification.Size = UDim2.new(0, 300, 0, 80)
    notification.Position = UDim2.new(0.5, -150, 0, -100)
    notification.BackgroundColor3 = Color3.fromRGB(85, 170, 85)
    notification.Parent = player.PlayerGui
    
    local notifCorner = Instance.new("UICorner")
    notifCorner.CornerRadius = UDim.new(0, 12)
    notifCorner.Parent = notification
    
    local notifText = Instance.new("TextLabel")
    notifText.Size = UDim2.new(1, 0, 1, 0)
    notifText.BackgroundTransparency = 1
    notifText.Text = "Plot " .. plotData.plotId .. " Assigned!"
    notifText.TextColor3 = Color3.new(1,1,1)
    notifText.TextScaled = true
    notifText.Font = Enum.Font.GothamBold
    notifText.Parent = notification
    
    -- Animate notification
    local tween1 = TweenService:Create(
        notification,
        TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
        {Position = UDim2.new(0.5, -150, 0, 20)}
    )
    
    local tween2 = TweenService:Create(
        notification,
        TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.In),
        {Position = UDim2.new(0.5, -150, 0, -100)}
    )
    
    tween1:Play()
    tween1.Completed:Connect(function()
        wait(2)
        tween2:Play()
        tween2.Completed:Connect(function()
            notification:Destroy()
        end)
    end)
end

function PlotController.getCurrentPlot()
    return currentPlot
end

function PlotController.cleanup()
    if plotUI then
        plotUI:Destroy()
        plotUI = nil
    end
    
    currentPlot = nil
    plotList = {}
    
    print("PlotController: Cleaned up")
end

return PlotController
