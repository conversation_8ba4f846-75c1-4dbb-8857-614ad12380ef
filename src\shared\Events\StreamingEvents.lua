-- StreamingEvents.lua
-- RemoteEvents for streaming functionality

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local StreamingEvents = {}

-- Create RemoteEvents for streaming
local function createRemoteEvent(name)
    local event = Instance.new("RemoteEvent")
    event.Name = name
    event.Parent = ReplicatedStorage
    return event
end

local function createRemoteFunction(name)
    local func = Instance.new("RemoteFunction")
    func.Name = name
    func.Parent = ReplicatedStorage
    return func
end

-- Streaming Events
StreamingEvents.StartStream = createRemoteEvent("StartStream")
StreamingEvents.StopStream = createRemoteEvent("StopStream")
StreamingEvents.UpdateStreamStats = createRemoteEvent("UpdateStreamStats")
StreamingEvents.StreamEarningsUpdate = createRemoteEvent("StreamEarningsUpdate")

-- Streaming Functions (for immediate responses)
StreamingEvents.GetStreamStatus = createRemoteFunction("GetStreamStatus")
StreamingEvents.CanStartStream = createRemoteFunction("CanStartStream")

-- Event descriptions for documentation
StreamingEvents.EventDescriptions = {
    StartStream = "Fired when player starts a streaming session",
    StopStream = "Fired when player stops streaming",
    UpdateStreamStats = "Fired to update real-time streaming statistics",
    StreamEarningsUpdate = "Fired to update player earnings from streaming",
    GetStreamStatus = "Function to get current streaming status",
    CanStartStream = "Function to check if player can start streaming"
}

return StreamingEvents
