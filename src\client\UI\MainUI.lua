-- MainUI.lua
-- Main user interface controller with all tabs

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

local MainUI = {}
local player = Players.LocalPlayer

-- UI state
local mainGui = nil
local currentTab = "Shop"
local isUIOpen = false
local tabButtons = {}
local tabFrames = {}

-- Tab configuration
local TABS = {
    {name = "Shop", icon = "🛍️", color = Color3.fromRGB(85, 170, 85)},
    {name = "House", icon = "🏡", color = Color3.fromRGB(170, 85, 85)},
    {name = "Dummy", icon = "🤖", color = Color3.fromRGB(85, 85, 170)},
    {name = "Stats", icon = "📈", color = Color3.fromRGB(170, 170, 85)},
    {name = "Inventory", icon = "🎮", color = Color3.fromRGB(170, 85, 170)},
    {name = "Settings", icon = "⚙️", color = Color3.fromRGB(85, 170, 170)},
    {name = "Leaderboard", icon = "🏆", color = Color3.fromRGB(255, 215, 0)},
    {name = "Social", icon = "👥", color = Color3.fromRGB(255, 100, 100)}
}

function MainUI.initialize()
    print("MainUI: Initializing...")
    
    -- Create main UI
    MainUI.createMainUI()
    
    -- Setup input handling
    MainUI.setupInputHandling()
    
    -- Initialize all tabs
    MainUI.initializeTabs()
    
    print("MainUI: Initialized successfully")
end

function MainUI.createMainUI()
    -- Create main ScreenGui
    mainGui = Instance.new("ScreenGui")
    mainGui.Name = "MainUI"
    mainGui.ResetOnSpawn = false
    mainGui.Parent = player.PlayerGui
    
    -- Create main frame
    local mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 800, 0, 600)
    mainFrame.Position = UDim2.new(0.5, -400, 0.5, -300)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    mainFrame.BackgroundTransparency = 0.1
    mainFrame.Visible = false
    mainFrame.Parent = mainGui
    
    local mainCorner = Instance.new("UICorner")
    mainCorner.CornerRadius = UDim.new(0, 15)
    mainCorner.Parent = mainFrame
    
    -- Create tab bar
    local tabBar = Instance.new("Frame")
    tabBar.Name = "TabBar"
    tabBar.Size = UDim2.new(1, 0, 0, 60)
    tabBar.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    tabBar.Parent = mainFrame
    
    local tabBarCorner = Instance.new("UICorner")
    tabBarCorner.CornerRadius = UDim.new(0, 15)
    tabBarCorner.Parent = tabBar
    
    -- Create tab bar layout
    local tabLayout = Instance.new("UIListLayout")
    tabLayout.FillDirection = Enum.FillDirection.Horizontal
    tabLayout.HorizontalAlignment = Enum.HorizontalAlignment.Center
    tabLayout.VerticalAlignment = Enum.VerticalAlignment.Center
    tabLayout.Padding = UDim.new(0, 5)
    tabLayout.Parent = tabBar
    
    -- Create tab buttons
    for _, tabConfig in ipairs(TABS) do
        local tabButton = MainUI.createTabButton(tabConfig)
        tabButton.Parent = tabBar
        tabButtons[tabConfig.name] = tabButton
    end
    
    -- Create content area
    local contentArea = Instance.new("Frame")
    contentArea.Name = "ContentArea"
    contentArea.Size = UDim2.new(1, -20, 1, -80)
    contentArea.Position = UDim2.new(0, 10, 0, 70)
    contentArea.BackgroundTransparency = 1
    contentArea.Parent = mainFrame
    
    -- Create close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.white
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.GothamBold
    closeButton.Parent = mainFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 6)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        MainUI.toggleUI()
    end)
    
    -- Create open button (always visible)
    local openButton = Instance.new("TextButton")
    openButton.Name = "OpenButton"
    openButton.Size = UDim2.new(0, 60, 0, 60)
    openButton.Position = UDim2.new(0, 20, 0, 20)
    openButton.BackgroundColor3 = Color3.fromRGB(85, 170, 85)
    openButton.Text = "📱"
    openButton.TextColor3 = Color3.white
    openButton.TextScaled = true
    openButton.Font = Enum.Font.GothamBold
    openButton.Parent = mainGui
    
    local openCorner = Instance.new("UICorner")
    openCorner.CornerRadius = UDim.new(0, 30)
    openCorner.Parent = openButton
    
    openButton.MouseButton1Click:Connect(function()
        MainUI.toggleUI()
    end)
end

function MainUI.createTabButton(tabConfig)
    local button = Instance.new("TextButton")
    button.Name = tabConfig.name .. "Tab"
    button.Size = UDim2.new(0, 90, 0, 50)
    button.BackgroundColor3 = tabConfig.color
    button.Text = tabConfig.icon .. "\n" .. tabConfig.name
    button.TextColor3 = Color3.white
    button.TextScaled = true
    button.Font = Enum.Font.Gotham
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = button
    
    button.MouseButton1Click:Connect(function()
        MainUI.switchTab(tabConfig.name)
    end)
    
    return button
end

function MainUI.setupInputHandling()
    -- Toggle UI with M key
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.M then
            MainUI.toggleUI()
        elseif input.KeyCode == Enum.KeyCode.Escape and isUIOpen then
            MainUI.toggleUI()
        end
    end)
end

function MainUI.initializeTabs()
    if not mainGui then return end
    
    local contentArea = mainGui.MainFrame:FindFirstChild("ContentArea")
    if not contentArea then return end
    
    -- Create tab frames
    for _, tabConfig in ipairs(TABS) do
        local tabFrame = MainUI.createTabFrame(tabConfig.name)
        tabFrame.Parent = contentArea
        tabFrames[tabConfig.name] = tabFrame
    end
    
    -- Show default tab
    MainUI.switchTab(currentTab)
end

function MainUI.createTabFrame(tabName)
    local frame = Instance.new("Frame")
    frame.Name = tabName .. "Frame"
    frame.Size = UDim2.new(1, 0, 1, 0)
    frame.BackgroundTransparency = 1
    frame.Visible = false
    
    -- Create tab-specific content
    if tabName == "Shop" then
        MainUI.createShopContent(frame)
    elseif tabName == "House" then
        MainUI.createHouseContent(frame)
    elseif tabName == "Dummy" then
        MainUI.createDummyContent(frame)
    elseif tabName == "Stats" then
        MainUI.createStatsContent(frame)
    elseif tabName == "Inventory" then
        MainUI.createInventoryContent(frame)
    elseif tabName == "Settings" then
        MainUI.createSettingsContent(frame)
    elseif tabName == "Leaderboard" then
        MainUI.createLeaderboardContent(frame)
    elseif tabName == "Social" then
        MainUI.createSocialContent(frame)
    end
    
    return frame
end

function MainUI.createShopContent(parent)
    -- Shop title
    local title = Instance.new("TextLabel")
    title.Name = "ShopTitle"
    title.Size = UDim2.new(1, 0, 0, 40)
    title.BackgroundTransparency = 1
    title.Text = "🛍️ Equipment Shop"
    title.TextColor3 = Color3.white
    title.TextScaled = true
    title.Font = Enum.Font.GothamBold
    title.Parent = parent
    
    -- Categories frame
    local categoriesFrame = Instance.new("Frame")
    categoriesFrame.Name = "CategoriesFrame"
    categoriesFrame.Size = UDim2.new(1, 0, 0, 50)
    categoriesFrame.Position = UDim2.new(0, 0, 0, 50)
    categoriesFrame.BackgroundTransparency = 1
    categoriesFrame.Parent = parent
    
    local categoriesLayout = Instance.new("UIListLayout")
    categoriesLayout.FillDirection = Enum.FillDirection.Horizontal
    categoriesLayout.HorizontalAlignment = Enum.HorizontalAlignment.Center
    categoriesLayout.Padding = UDim.new(0, 10)
    categoriesLayout.Parent = categoriesFrame
    
    -- Create category buttons
    local categories = {"Essential", "Audio", "Video", "Comfort", "Visual", "Furniture"}
    for _, category in ipairs(categories) do
        local categoryButton = Instance.new("TextButton")
        categoryButton.Name = category .. "Button"
        categoryButton.Size = UDim2.new(0, 100, 0, 40)
        categoryButton.BackgroundColor3 = Color3.fromRGB(70, 70, 70)
        categoryButton.Text = category
        categoryButton.TextColor3 = Color3.white
        categoryButton.TextScaled = true
        categoryButton.Font = Enum.Font.Gotham
        categoryButton.Parent = categoriesFrame
        
        local corner = Instance.new("UICorner")
        corner.CornerRadius = UDim.new(0, 6)
        corner.Parent = categoryButton
        
        categoryButton.MouseButton1Click:Connect(function()
            MainUI.filterShopByCategory(category)
        end)
    end
    
    -- Items scroll frame
    local itemsFrame = Instance.new("ScrollingFrame")
    itemsFrame.Name = "ItemsFrame"
    itemsFrame.Size = UDim2.new(1, 0, 1, -110)
    itemsFrame.Position = UDim2.new(0, 0, 0, 110)
    itemsFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    itemsFrame.BackgroundTransparency = 0.5
    itemsFrame.ScrollBarThickness = 8
    itemsFrame.Parent = parent
    
    local itemsCorner = Instance.new("UICorner")
    itemsCorner.CornerRadius = UDim.new(0, 8)
    itemsCorner.Parent = itemsFrame
    
    local itemsLayout = Instance.new("UIGridLayout")
    itemsLayout.CellSize = UDim2.new(0, 180, 0, 120)
    itemsLayout.CellPadding = UDim2.new(0, 10, 0, 10)
    itemsLayout.Parent = itemsFrame
    
    -- Populate with items
    MainUI.populateShopItems(itemsFrame)
end

function MainUI.populateShopItems(parent)
    -- Create shop items based on GameConfig
    for equipType, equipConfig in pairs(GameConfig.EquipmentTypes) do
        for level, levelData in ipairs(equipConfig.levels) do
            local itemFrame = MainUI.createShopItem(equipType, level, levelData, equipConfig)
            itemFrame.Parent = parent
        end
    end

    -- Update canvas size
    local layout = parent:FindFirstChild("UIGridLayout")
    if layout then
        parent.CanvasSize = UDim2.new(0, 0, 0, layout.AbsoluteContentSize.Y + 20)
    end
end

function MainUI.createShopItem(equipType, level, levelData, equipConfig)
    local itemFrame = Instance.new("Frame")
    itemFrame.Name = equipType .. "_" .. level
    itemFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = itemFrame
    
    -- Item name
    local itemName = Instance.new("TextLabel")
    itemName.Size = UDim2.new(1, 0, 0, 25)
    itemName.BackgroundTransparency = 1
    itemName.Text = levelData.name
    itemName.TextColor3 = Color3.white
    itemName.TextScaled = true
    itemName.Font = Enum.Font.GothamBold
    itemName.Parent = itemFrame
    
    -- Item description
    local itemDesc = Instance.new("TextLabel")
    itemDesc.Size = UDim2.new(1, -10, 0, 40)
    itemDesc.Position = UDim2.new(0, 5, 0, 25)
    itemDesc.BackgroundTransparency = 1
    itemDesc.Text = levelData.description
    itemDesc.TextColor3 = Color3.fromRGB(200, 200, 200)
    itemDesc.TextScaled = true
    itemDesc.Font = Enum.Font.Gotham
    itemDesc.TextWrapped = true
    itemDesc.Parent = itemFrame
    
    -- Price and buy button
    local buyButton = Instance.new("TextButton")
    buyButton.Size = UDim2.new(1, -10, 0, 30)
    buyButton.Position = UDim2.new(0, 5, 1, -35)
    buyButton.BackgroundColor3 = Color3.fromRGB(85, 170, 85)
    buyButton.Text = "Buy - $" .. levelData.price
    buyButton.TextColor3 = Color3.white
    buyButton.TextScaled = true
    buyButton.Font = Enum.Font.GothamBold
    buyButton.Parent = itemFrame
    
    local buyCorner = Instance.new("UICorner")
    buyCorner.CornerRadius = UDim.new(0, 6)
    buyCorner.Parent = buyButton
    
    buyButton.MouseButton1Click:Connect(function()
        MainUI.purchaseItem(equipType, level, levelData.price)
    end)
    
    return itemFrame
end

-- Placeholder functions for other tabs
function MainUI.createHouseContent(parent)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🏡 House Management\n(Coming Soon)"
    label.TextColor3 = Color3.white
    label.TextScaled = true
    label.Font = Enum.Font.GothamBold
    label.Parent = parent
end

function MainUI.createDummyContent(parent)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🤖 Dummy Customization\n(Coming Soon)"
    label.TextColor3 = Color3.white
    label.TextScaled = true
    label.Font = Enum.Font.GothamBold
    label.Parent = parent
end

function MainUI.createStatsContent(parent)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "📈 Player Statistics\n(Coming Soon)"
    label.TextColor3 = Color3.white
    label.TextScaled = true
    label.Font = Enum.Font.GothamBold
    label.Parent = parent
end

function MainUI.createInventoryContent(parent)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🎮 Inventory Management\n(Coming Soon)"
    label.TextColor3 = Color3.white
    label.TextScaled = true
    label.Font = Enum.Font.GothamBold
    label.Parent = parent
end

function MainUI.createSettingsContent(parent)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "⚙️ Game Settings\n(Coming Soon)"
    label.TextColor3 = Color3.white
    label.TextScaled = true
    label.Font = Enum.Font.GothamBold
    label.Parent = parent
end

function MainUI.createLeaderboardContent(parent)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "🏆 Global Leaderboard\n(Coming Soon)"
    label.TextColor3 = Color3.white
    label.TextScaled = true
    label.Font = Enum.Font.GothamBold
    label.Parent = parent
end

function MainUI.createSocialContent(parent)
    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = "👥 Social Features\n(Coming Soon)"
    label.TextColor3 = Color3.white
    label.TextScaled = true
    label.Font = Enum.Font.GothamBold
    label.Parent = parent
end

function MainUI.toggleUI()
    if not mainGui then return end
    
    local mainFrame = mainGui:FindFirstChild("MainFrame")
    local openButton = mainGui:FindFirstChild("OpenButton")
    
    if not mainFrame or not openButton then return end
    
    isUIOpen = not isUIOpen
    
    if isUIOpen then
        mainFrame.Visible = true
        openButton.Visible = false
        
        -- Animate opening
        mainFrame.Size = UDim2.new(0, 0, 0, 0)
        mainFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
        
        local tween = TweenService:Create(
            mainFrame,
            TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
            {
                Size = UDim2.new(0, 800, 0, 600),
                Position = UDim2.new(0.5, -400, 0.5, -300)
            }
        )
        tween:Play()
    else
        openButton.Visible = true
        
        -- Animate closing
        local tween = TweenService:Create(
            mainFrame,
            TweenInfo.new(0.2, Enum.EasingStyle.Back, Enum.EasingDirection.In),
            {
                Size = UDim2.new(0, 0, 0, 0),
                Position = UDim2.new(0.5, 0, 0.5, 0)
            }
        )
        tween:Play()
        tween.Completed:Connect(function()
            mainFrame.Visible = false
        end)
    end
end

function MainUI.switchTab(tabName)
    if not tabFrames[tabName] then return end
    
    -- Hide all tabs
    for _, frame in pairs(tabFrames) do
        frame.Visible = false
    end
    
    -- Show selected tab
    tabFrames[tabName].Visible = true
    currentTab = tabName
    
    -- Update tab button appearances
    for name, button in pairs(tabButtons) do
        if name == tabName then
            button.BackgroundTransparency = 0
        else
            button.BackgroundTransparency = 0.3
        end
    end
end

function MainUI.filterShopByCategory(category)
    -- TODO: Implement category filtering
    print("Filtering shop by category:", category)
end

function MainUI.purchaseItem(equipType, level, price)
    -- Send purchase request to server
    EventManager.Shop.PurchaseItem:FireServer({
        itemType = equipType,
        level = level,
        price = price
    })
    
    print("Purchasing", equipType, "level", level, "for $" .. price)
end

function MainUI.cleanup()
    if mainGui then
        mainGui:Destroy()
        mainGui = nil
    end
    
    tabButtons = {}
    tabFrames = {}
    isUIOpen = false
    
    print("MainUI: Cleaned up")
end

return MainUI
