-- ShopEvents.lua
-- RemoteEvents for shop and purchase system

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local ShopEvents = {}

-- Create RemoteEvents for shop
local function createRemoteEvent(name)
    local event = Instance.new("RemoteEvent")
    event.Name = name
    event.Parent = ReplicatedStorage
    return event
end

local function createRemoteFunction(name)
    local func = Instance.new("RemoteFunction")
    func.Name = name
    func.Parent = ReplicatedStorage
    return func
end

-- Shop Events
ShopEvents.PurchaseItem = createRemoteEvent("PurchaseItem")
ShopEvents.PurchaseStudio = createRemoteEvent("PurchaseStudio")
ShopEvents.PurchaseGamepass = createRemoteEvent("PurchaseGamepass")
ShopEvents.RefreshShop = createRemoteEvent("RefreshShop")
ShopEvents.PurchaseSuccess = createRemoteEvent("PurchaseSuccess")
ShopEvents.PurchaseFailed = createRemoteEvent("PurchaseFailed")

-- Shop Functions
ShopEvents.GetShopItems = createRemoteFunction("GetShopItems")
ShopEvents.GetPlayerCurrency = createRemoteFunction("GetPlayerCurrency")
ShopEvents.CanAffordItem = createRemoteFunction("CanAffordItem")
ShopEvents.GetGamepassInfo = createRemoteFunction("GetGamepassInfo")

-- Currency Events
ShopEvents.CurrencyUpdate = createRemoteEvent("CurrencyUpdate")
ShopEvents.AddCurrency = createRemoteEvent("AddCurrency")

-- Event descriptions
ShopEvents.EventDescriptions = {
    PurchaseItem = "Fired when player purchases equipment",
    PurchaseStudio = "Fired when player purchases a studio",
    PurchaseGamepass = "Fired when player purchases a gamepass",
    RefreshShop = "Fired to refresh shop inventory",
    PurchaseSuccess = "Fired when purchase is successful",
    PurchaseFailed = "Fired when purchase fails",
    GetShopItems = "Function to get available shop items",
    GetPlayerCurrency = "Function to get player's currency balances",
    CanAffordItem = "Function to check if player can afford an item",
    GetGamepassInfo = "Function to get gamepass information",
    CurrencyUpdate = "Fired when player currency changes",
    AddCurrency = "Fired to add currency to player"
}

return ShopEvents
