local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

local PlayerManager = require(script.Parent.PlayerManager)
local StreamSystem = require(script.Parent.StreamSystem)
local DataManager = require(ReplicatedStorage.Shared.Modules.DataManager)
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)

local GameManager = {}
GameManager.__index = GameManager

function GameManager.new()
    local self = setmetatable({}, GameManager)
    
    -- Initialize systems
    self.playerManager = PlayerManager.new()
    self.streamSystem = StreamSystem.new(self.playerManager)
    
    -- Set up auto-save
    self:setupAutoSave()
    
    -- Set up player cleanup
    game.Players.PlayerRemoving:Connect(function(player)
        self:playerRemoving(player)
    end)
    
    return self
end

function GameManager:setupAutoSave()
    local saveInterval = GameConfig.Settings.AutoSaveInterval
    
    -- Save all players' data at regular intervals
    spawn(function()
        while true do
            wait(saveInterval)
            self:saveAllPlayers()
        end
    end)
end

function GameManager:saveAllPlayers()
    for _, player in ipairs(game.Players:GetPlayers()) do
        local playerData = self.playerManager:getPlayerData(player)
        if playerData and playerData.dataManager then
            playerData.dataManager:saveData()
        end
    end
end

function GameManager:playerRemoving(player)
    -- Stop any active streams
    self.streamSystem:playerRemoving(player)
    
    -- Save player data
    local playerData = self.playerManager:getPlayerData(player)
    if playerData and playerData.dataManager then
        playerData.dataManager:saveData()
    end
end

-- Clean up when game is shutting down
function GameManager:shutdown()
    -- Save all players' data
    self:saveAllPlayers()
    
    -- Clean up systems
    self.playerManager:shutdown()
end

-- Initialize the game
local function init()
    local gameManager = GameManager.new()
    
    -- Handle game close
    game:BindToClose(function()
        gameManager:shutdown()
    end)
    
    return gameManager
end

-- Start the game
return init()
