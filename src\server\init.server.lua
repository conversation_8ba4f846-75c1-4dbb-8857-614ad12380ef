-- init.server.lua
-- Main server initialization script for Grown A Streamer

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

-- Import core systems
local PlayerManager = require(script.Core.PlayerManager)
local PlotManager = require(script.Core.PlotManager)

-- Import game systems
local StreamingSystem = require(script.Systems.StreamingSystem)
local PlacementSystem = require(script.Systems.PlacementSystem)

-- Import handlers
local ShopHandler = require(script.Handlers.ShopHandler)

-- Import services
local MarketplaceHandler = require(script.Services.MarketplaceService)

-- Main server module
local Server = {}

function Server.initialize()
    print("=== GROWN A STREAMER SERVER STARTING ===")
    print("Game Version:", GameConfig.Settings.Version or "1.0.0")
    print("Total Studios:", #GameConfig.StudioTypes)
    print("Total Equipment Types:", #GameConfig.EquipmentTypes)
    print("Total Plots:", GameConfig.Settings.TotalPlots)
    
    -- Initialize shared systems first
    print("\n--- Initializing Shared Systems ---")
    EventManager.initialize()
    
    -- Initialize core systems
    print("\n--- Initializing Core Systems ---")
    PlayerManager.initialize()
    PlotManager.initialize()
    
    -- Initialize game systems
    print("\n--- Initializing Game Systems ---")
    StreamingSystem.initialize()
    PlacementSystem.initialize()
    
    -- Initialize handlers
    print("\n--- Initializing Handlers ---")
    ShopHandler.initialize()
    
    -- Initialize services
    print("\n--- Initializing Services ---")
    MarketplaceHandler.initialize()
    
    -- Setup cleanup on server shutdown
    game:BindToClose(function()
        Server.cleanup()
    end)
    
    print("\n=== GROWN A STREAMER SERVER READY ===")
    print("Server initialized successfully!")
    print("Players can now join and start their streaming journey!")
end

function Server.cleanup()
    print("\n=== GROWN A STREAMER SERVER SHUTTING DOWN ===")
    
    -- Cleanup all systems in reverse order
    MarketplaceHandler.cleanup()
    ShopHandler.cleanup()
    PlacementSystem.cleanup()
    StreamingSystem.cleanup()
    PlotManager.cleanup()
    PlayerManager.cleanup()
    EventManager.cleanup()
    
    print("Server cleanup completed!")
end

-- Initialize the server
Server.initialize()

return Server
