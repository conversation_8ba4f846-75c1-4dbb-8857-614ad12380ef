# 🚀 Grown A Streamer - Startup Guide

## ✅ **Pre-Launch Checklist**

### 1. **🔧 Rojo Setup**
- [ ] Install Rojo 7.x: `cargo install rojo`
- [ ] Install Rojo Studio plugin
- [ ] Navigate to project directory: `cd "GROWN STREAM"`
- [ ] Start Rojo server: `rojo serve`
- [ ] Connect Studio to localhost:34872

### 2. **🎮 Studio Setup**
- [ ] Open Roblox Studio
- [ ] Create new place or open existing
- [ ] Connect to Rojo (should see green connection)
- [ ] Sync all files (should see folder structure appear)

### 3. **⚙️ Game Configuration**
- [ ] Check ServerScriptService has `init.server.lua`
- [ ] Check StarterPlayerScripts has `init.client.lua`
- [ ] Verify ReplicatedStorage has Shared folder
- [ ] Check Workspace is empty (plots will be created automatically)

### 4. **🧪 Initial Testing**
- [ ] Run TestScript.server.lua (should be in ServerScriptService)
- [ ] Check Output for test results
- [ ] All 4 tests should pass ✅

## 🎯 **Launch Sequence**

### Step 1: Start the Server
1. **Press Play** in Studio (or F5)
2. **Check Output** for initialization messages:
   ```
   === GROWN A STREAMER SERVER STARTING ===
   Game Version: 1.0.0
   Total Studios: 8
   Total Equipment Types: 6
   Total Plots: 8
   
   --- Initializing Shared Systems ---
   EventManager: Initializing...
   
   --- Initializing Core Systems ---
   PlayerManager: Initializing...
   PlotManager: Initializing...
   
   --- Initializing Game Systems ---
   StreamingSystem: Initializing...
   PlacementSystem: Initializing...
   
   --- Initializing Handlers ---
   ShopHandler: Initializing...
   
   --- Initializing Services ---
   MarketplaceHandler: Initializing...
   
   === GROWN A STREAMER SERVER READY ===
   ```

### Step 2: Test Client Connection
1. **Join the game** (click Play button in Studio)
2. **Check client output**:
   ```
   === GROWN A STREAMER CLIENT STARTING ===
   Player: [YourName]
   UserId: [YourUserId]
   
   --- Initializing Shared Systems ---
   EventManager: Initializing...
   
   --- Initializing Client Systems ---
   PlacementSystem (Client): Initializing...
   
   --- Initializing Controllers ---
   StreamController: Initializing...
   PlotController: Initializing...
   
   --- Initializing User Interface ---
   MainUI: Initializing...
   
   === GROWN A STREAMER CLIENT READY ===
   ```

### Step 3: Verify Core Features
- [ ] **Plot Assignment**: You should be assigned Plot 1-8 automatically
- [ ] **Welcome Message**: Should appear for 10 seconds
- [ ] **Main UI**: Press M to open menu (8 tabs visible)
- [ ] **Plot Teleport**: Should see plot info panel on right side
- [ ] **Workspace**: Should see Plot1-8 folders with structures

## 🎮 **Basic Gameplay Test**

### Test 1: UI Navigation
1. **Press M** to open main menu
2. **Click each tab**: Shop, House, Dummy, Stats, Inventory, Settings, Leaderboard, Social
3. **Shop tab** should show equipment with prices
4. **Close menu** with X or Escape

### Test 2: Plot System
1. **Check plot info panel** (top right)
2. **Click "Teleport to Plot"** button
3. **Should teleport** to your assigned plot
4. **See plot structures**: Base, spawn point, teleport pad, sign

### Test 3: Placement System
1. **Walk to placement zones** (black parts on your plot)
2. **Get close to trigger** ProximityPrompt
3. **Press E** to interact (should show item selector)
4. **Select an item** to place
5. **Use Q/E** to rotate, Enter to confirm

### Test 4: Streaming System
1. **Look for stream button** (bottom left of screen)
2. **Click "Start Stream"** 
3. **Should see**: "🔴 LIVE" indicator and stats panel
4. **Watch numbers increase**: Viewers, Likes, Followers, Earnings
5. **Click "Stop Stream"** to end

## 🔧 **Troubleshooting**

### Common Issues:

#### ❌ "Instance is not a supported attribute type"
- **Fixed**: This was resolved in PlotManager.lua
- **If still occurs**: Check for any SetAttribute calls with Instance objects

#### ❌ "Module not found" errors
- **Solution**: Ensure Rojo is connected and synced
- **Check**: ReplicatedStorage.Shared folder exists
- **Restart**: Rojo server and reconnect Studio

#### ❌ No plots appear
- **Check**: PlotManager initialization in output
- **Verify**: Workspace.Plots folder exists
- **Debug**: Look for PlotManager error messages

#### ❌ UI doesn't open with M key
- **Check**: Client initialization completed
- **Verify**: MainUI module loaded successfully
- **Test**: Try clicking the 📱 button instead

#### ❌ DataStore errors
- **Note**: DataStore only works in published games
- **For testing**: Errors are normal in Studio
- **Solution**: Publish game to test DataStore functionality

### Debug Commands:
```lua
-- In Studio Command Bar:
print("Testing GameConfig:")
local GameConfig = require(game.ReplicatedStorage.Shared.Modules.GameConfig)
print("Studios:", #GameConfig.StudioTypes)

-- Check if player has plot:
local PlotManager = require(game.ServerScriptService.Core.PlotManager)
print("Player plots:", PlotManager.getAllPlots())
```

## 🎉 **Success Indicators**

### ✅ **Game is Working When:**
1. **Server starts** without errors
2. **Client connects** and shows welcome message
3. **Plot is assigned** automatically
4. **UI opens** with M key
5. **Placement zones** respond to interaction
6. **Streaming button** appears and functions
7. **Shop** shows items with prices
8. **No red errors** in Output window

### 🎯 **Ready for Players When:**
- [ ] All basic tests pass
- [ ] Multiple players can join simultaneously
- [ ] Plots assign correctly to different players
- [ ] No memory leaks or performance issues
- [ ] DataStore works (after publishing)

## 📞 **Need Help?**

If you encounter issues:
1. **Check Output window** for error messages
2. **Verify Rojo connection** (green indicator)
3. **Restart Studio** and Rojo server
4. **Check file structure** matches project.md
5. **Review this guide** step by step

**The game should now be fully functional and ready for players! 🎮**
