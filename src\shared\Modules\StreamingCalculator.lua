-- StreamingCalculator.lua
-- Advanced streaming calculations and algorithms

local GameConfig = require(script.Parent.GameConfig)

local StreamingCalculator = {}

-- Algorithm constants
local VIEWER_GROWTH_RATE = 0.1 -- How fast viewers grow during stream
local VIEWER_DECAY_RATE = 0.05 -- How fast viewers leave if not engaged
local ENGAGEMENT_THRESHOLD = 0.7 -- Minimum engagement to maintain viewers
local VIRAL_THRESHOLD = 1000 -- Viewer count where viral growth kicks in
local VIRAL_MULTIPLIER = 1.5 -- Multiplier for viral growth

-- Calculate base viewer count based on equipment and studio
function StreamingCalculator.calculateBaseViewers(studioType, equipment)
    local studioConfig = GameConfig.StudioTypes[studioType]
    if not studioConfig then return 5 end
    
    local baseViewers = studioConfig.baseViewers
    local totalMultiplier = 1
    
    -- Apply equipment multipliers
    for equipType, equipData in pairs(equipment or {}) do
        local equipConfig = GameConfig.getEquipmentLevel(equipType, equipData.level or 1)
        if equipConfig then
            totalMultiplier = totalMultiplier * equipConfig.multiplier
        end
    end
    
    return math.floor(baseViewers * totalMultiplier)
end

-- Calculate viewer growth over time
function StreamingCalculator.calculateViewerGrowth(currentViewers, baseViewers, duration, engagement)
    local growthRate = VIEWER_GROWTH_RATE
    
    -- Boost growth rate based on engagement
    if engagement > ENGAGEMENT_THRESHOLD then
        growthRate = growthRate * (1 + engagement)
    end
    
    -- Apply viral growth if threshold is reached
    if currentViewers >= VIRAL_THRESHOLD then
        growthRate = growthRate * VIRAL_MULTIPLIER
    end
    
    -- Calculate growth based on duration (diminishing returns)
    local timeMultiplier = math.min(1 + (duration / 3600), 2) -- Max 2x after 1 hour
    local targetViewers = baseViewers * timeMultiplier
    
    -- Smooth growth towards target
    local growth = (targetViewers - currentViewers) * growthRate
    
    return math.max(0, growth)
end

-- Calculate viewer decay (people leaving)
function StreamingCalculator.calculateViewerDecay(currentViewers, engagement)
    if engagement >= ENGAGEMENT_THRESHOLD then
        return 0 -- No decay if well engaged
    end
    
    local decayRate = VIEWER_DECAY_RATE * (1 - engagement)
    return math.floor(currentViewers * decayRate)
end

-- Calculate engagement based on equipment quality and stream duration
function StreamingCalculator.calculateEngagement(equipment, duration)
    local baseEngagement = 0.5
    local equipmentBonus = 0
    
    -- Calculate equipment contribution to engagement
    for equipType, equipData in pairs(equipment or {}) do
        local equipConfig = GameConfig.getEquipmentLevel(equipType, equipData.level or 1)
        if equipConfig then
            -- Higher tier equipment increases engagement
            equipmentBonus = equipmentBonus + (equipConfig.multiplier - 1) * 0.1
        end
    end
    
    -- Duration affects engagement (sweet spot around 30-60 minutes)
    local durationMinutes = duration / 60
    local durationMultiplier = 1
    
    if durationMinutes < 5 then
        durationMultiplier = 0.5 -- Too short
    elseif durationMinutes < 30 then
        durationMultiplier = durationMinutes / 30 -- Building up
    elseif durationMinutes <= 60 then
        durationMultiplier = 1 -- Sweet spot
    else
        durationMultiplier = math.max(0.3, 1 - ((durationMinutes - 60) / 120)) -- Declining
    end
    
    local finalEngagement = (baseEngagement + equipmentBonus) * durationMultiplier
    return math.min(1, math.max(0, finalEngagement))
end

-- Calculate likes based on viewers and engagement
function StreamingCalculator.calculateLikes(viewers, engagement, deltaTime)
    local baseLikeChance = GameConfig.Economy.BaseLikeChance
    local engagementMultiplier = 1 + engagement
    local likeChance = baseLikeChance * engagementMultiplier
    
    -- Calculate expected likes per second
    local likesPerSecond = viewers * likeChance / 60 -- Normalize to per-minute rate
    return math.floor(likesPerSecond * deltaTime)
end

-- Calculate followers based on likes and stream quality
function StreamingCalculator.calculateFollowers(totalLikes, viewers, engagement)
    local baseFollowChance = GameConfig.Economy.BaseFollowChance
    
    -- Higher engagement and like ratio increases follow chance
    local likeRatio = totalLikes / math.max(1, viewers)
    local followMultiplier = 1 + (engagement * 2) + (likeRatio * 0.5)
    
    local followChance = baseFollowChance * followMultiplier
    return math.floor(viewers * followChance)
end

-- Calculate cash earnings
function StreamingCalculator.calculateCashEarnings(viewers, engagement, deltaTime)
    local baseCashPerViewer = GameConfig.Economy.BaseCashPerViewer
    local engagementMultiplier = 1 + (engagement * 0.5)
    
    local cashPerSecond = viewers * baseCashPerViewer * engagementMultiplier / 60
    return cashPerSecond * deltaTime
end

-- Calculate experience points earned
function StreamingCalculator.calculateXPEarned(duration, viewers, engagement)
    local baseXP = GameConfig.Economy.BaseXPPerStream
    local durationBonus = math.min(duration / 1800, 2) -- Max 2x bonus after 30 minutes
    local viewerBonus = math.min(viewers / 100, 3) -- Max 3x bonus at 100+ viewers
    local engagementBonus = engagement * 2
    
    return math.floor(baseXP * (1 + durationBonus + viewerBonus + engagementBonus))
end

-- Calculate clout earned (prestige currency)
function StreamingCalculator.calculateCloutEarned(viewers, likes, followers, duration)
    local baseClout = GameConfig.Economy.BaseCloutPerStream
    
    -- Clout is earned based on overall performance
    local viewerBonus = math.min(viewers / 50, 5) -- Max 5x at 250+ viewers
    local likeBonus = math.min(likes / 20, 3) -- Max 3x at 60+ likes
    local followerBonus = followers * 2 -- 2 clout per follower
    local durationBonus = math.min(duration / 3600, 1) -- Max 1x bonus after 1 hour
    
    return math.floor(baseClout * (1 + viewerBonus + likeBonus + durationBonus) + followerBonus)
end

-- Main calculation function that ties everything together
function StreamingCalculator.calculateStreamPerformance(streamData, deltaTime)
    local currentViewers = streamData.currentViewers or 0
    local baseViewers = streamData.baseViewers or 5
    local duration = streamData.duration or 0
    local equipment = streamData.equipment or {}
    local totalLikes = streamData.totalLikes or 0
    
    -- Calculate engagement
    local engagement = StreamingCalculator.calculateEngagement(equipment, duration)
    
    -- Calculate viewer changes
    local viewerGrowth = StreamingCalculator.calculateViewerGrowth(
        currentViewers, baseViewers, duration, engagement
    )
    local viewerDecay = StreamingCalculator.calculateViewerDecay(currentViewers, engagement)
    
    -- Update viewer count
    local newViewers = math.max(1, currentViewers + viewerGrowth - viewerDecay)
    
    -- Calculate earnings
    local likes = StreamingCalculator.calculateLikes(newViewers, engagement, deltaTime)
    local followers = StreamingCalculator.calculateFollowers(totalLikes + likes, newViewers, engagement)
    local cash = StreamingCalculator.calculateCashEarnings(newViewers, engagement, deltaTime)
    local xp = StreamingCalculator.calculateXPEarned(duration, newViewers, engagement)
    local clout = StreamingCalculator.calculateCloutEarned(newViewers, totalLikes + likes, followers, duration)
    
    return {
        viewers = math.floor(newViewers),
        likes = likes,
        followers = followers,
        cash = cash,
        xp = xp,
        clout = clout,
        engagement = engagement,
        viewerGrowth = viewerGrowth,
        viewerDecay = viewerDecay
    }
end

-- Utility function to get stream quality rating
function StreamingCalculator.getStreamQuality(equipment, studioType)
    local totalScore = 0
    local maxScore = 0
    
    -- Score equipment
    for equipType, equipData in pairs(equipment or {}) do
        local equipConfig = GameConfig.EquipmentTypes[equipType]
        if equipConfig then
            local level = equipData.level or 1
            local maxLevel = #equipConfig.levels
            totalScore = totalScore + (level / maxLevel) * 20
            maxScore = maxScore + 20
        end
    end
    
    -- Score studio
    local studioConfig = GameConfig.StudioTypes[studioType]
    if studioConfig then
        local studioScore = (studioConfig.id / 8) * 20 -- Max studio is 8
        totalScore = totalScore + studioScore
        maxScore = maxScore + 20
    end
    
    local qualityRating = totalScore / math.max(1, maxScore)
    
    if qualityRating >= 0.9 then return "Legendary" end
    if qualityRating >= 0.75 then return "Epic" end
    if qualityRating >= 0.6 then return "Rare" end
    if qualityRating >= 0.4 then return "Good" end
    if qualityRating >= 0.2 then return "Basic" end
    return "Poor"
end

-- Predict stream performance for planning
function StreamingCalculator.predictStreamPerformance(studioType, equipment, duration)
    local baseViewers = StreamingCalculator.calculateBaseViewers(studioType, equipment)
    local engagement = StreamingCalculator.calculateEngagement(equipment, duration)
    
    -- Simulate stream performance
    local simulatedData = {
        currentViewers = baseViewers,
        baseViewers = baseViewers,
        duration = duration,
        equipment = equipment,
        totalLikes = 0
    }
    
    local performance = StreamingCalculator.calculateStreamPerformance(simulatedData, duration)
    
    return {
        estimatedViewers = performance.viewers,
        estimatedEarnings = performance.cash * (duration / 60), -- Total for duration
        estimatedLikes = performance.likes * (duration / 60),
        estimatedFollowers = performance.followers,
        qualityRating = StreamingCalculator.getStreamQuality(equipment, studioType),
        engagement = engagement
    }
end

return StreamingCalculator
