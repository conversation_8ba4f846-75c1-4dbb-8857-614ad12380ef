local GameConfig = {}

-- Studio types and their configurations
GameConfig.StudioTypes = {
    DIRT_STUDIO = {
        id = 1,
        name = "Dirt Studio",
        price = 0,
        baseViewers = 5,
        maxEquipmentSlots = 4,
        unlockLevel = 1
    },
    WOODEN_CABIN = {
        id = 2,
        name = "Wooden Cabin",
        price = 5000,
        baseViewers = 15,
        maxEquipmentSlots = 6,
        unlockLevel = 5
    },
    -- Add other studio types here
}

-- Equipment types and their stats
GameConfig.EquipmentTypes = {
    PC = {
        baseViewerMultiplier = 1.2,
        levels = {
            {price = 100, multiplier = 1.0},
            {price = 500, multiplier = 1.5},
            {price = 2000, multiplier = 2.0}
        }
    },
    MIC = {
        baseViewerMultiplier = 1.1,
        levels = {
            {price = 50, multiplier = 1.2},
            {price = 200, multiplier = 1.5},
            {price = 1000, multiplier = 2.0}
        }
    },
    CAMERA = {
        baseViewerMultiplier = 1.1,
        levels = {
            {price = 75, multiplier = 1.1},
            {price = 300, multiplier = 1.4},
            {price = 1200, multiplier = 1.8}
        }
    },
    CHAIR = {
        baseViewerMultiplier = 1.05,
        levels = {
            {price = 40, multiplier = 1.1},
            {price = 150, multiplier = 1.3},
            {price = 600, multiplier = 1.6}
        }
    }
}

-- Game economy settings
GameConfig.Economy = {
    BaseViewerIncome = 1,
    BaseLikeChance = 0.2, -- 20% chance per viewer to like
    BaseFollowChance = 0.05, -- 5% chance per viewer to follow
    BaseCashPerViewer = 0.1,
    ViewerFalloffRate = 0.1, -- 10% of viewers leave every interval if not engaged
}

-- Game settings
GameConfig.Settings = {
    AutoSaveInterval = 60, -- seconds
    StreamTickRate = 5, -- seconds between stream updates
    MaxStreamLength = 3600, -- seconds (1 hour)
}

return GameConfig
