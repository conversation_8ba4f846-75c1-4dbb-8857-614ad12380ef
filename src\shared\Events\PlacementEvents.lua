-- PlacementEvents.lua
-- RemoteEvents for equipment placement system

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local PlacementEvents = {}

-- Create RemoteEvents for placement
local function createRemoteEvent(name)
    local event = Instance.new("RemoteEvent")
    event.Name = name
    event.Parent = ReplicatedStorage
    return event
end

local function createRemoteFunction(name)
    local func = Instance.new("RemoteFunction")
    func.Name = name
    func.Parent = ReplicatedStorage
    return func
end

-- Placement Events
PlacementEvents.StartPlacement = createRemoteEvent("StartPlacement")
PlacementEvents.ConfirmPlacement = createRemoteEvent("ConfirmPlacement")
PlacementEvents.CancelPlacement = createRemoteEvent("CancelPlacement")
PlacementEvents.RotateItem = createRemoteEvent("RotateItem")
PlacementEvents.DeleteItem = createRemoteEvent("DeleteItem")
PlacementEvents.MoveItem = createRemoteEvent("MoveItem")
PlacementEvents.PlacementUpdate = createRemoteEvent("PlacementUpdate")

-- Placement Functions
PlacementEvents.ValidatePlacement = createRemoteFunction("ValidatePlacement")
PlacementEvents.GetPlacementZones = createRemoteFunction("GetPlacementZones")
PlacementEvents.GetPlayerItems = createRemoteFunction("GetPlayerItems")

-- Event descriptions
PlacementEvents.EventDescriptions = {
    StartPlacement = "Fired when player starts placing an item",
    ConfirmPlacement = "Fired when player confirms item placement",
    CancelPlacement = "Fired when player cancels placement",
    RotateItem = "Fired when player rotates an item during placement",
    DeleteItem = "Fired when player deletes a placed item",
    MoveItem = "Fired when player moves an existing item",
    PlacementUpdate = "Fired to update placement state for all clients",
    ValidatePlacement = "Function to validate if placement is allowed",
    GetPlacementZones = "Function to get available placement zones",
    GetPlayerItems = "Function to get player's owned items"
}

return PlacementEvents
