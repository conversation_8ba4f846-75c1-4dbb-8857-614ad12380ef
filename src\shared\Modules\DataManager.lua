local DataStoreService = game:GetService("DataStoreService")
local GameConfig = require(script.Parent.GameConfig)

local DataManager = {}
DataManager.__index = DataManager

-- Constants
local DATASTORE_KEY = "GrownStreamerData_"
local DEFAULT_PLAYER_DATA = {
    cash = 0,
    gems = 0,
    level = 1,
    xp = 0,
    currentStudio = "DIRT_STUDIO",
    ownedStudios = {"DIRT_STUDIO"},
    equipment = {
        PC = {level = 1},
        MIC = {level = 1},
        CAMERA = {level = 1},
        CHAIR = {level = 1}
    },
    stats = {
        totalViews = 0,
        totalLikes = 0,
        totalFollowers = 0,
        totalEarnings = 0,
        streamsCompleted = 0,
        timeStreamed = 0
    }
}

-- Initialize DataStore
local playerDataStore = DataStoreService:GetDataStore("PlayerData")

-- Private functions
local function deepCopy(original)
    local copy = {}
    for k, v in pairs(original) do
        if type(v) == "table" then
            v = deepCopy(v)
        end
        copy[k] = v
    end
    return copy
end

-- Public methods
function DataManager.new(player)
    local self = setmetatable({}, DataManager)
    self.player = player
    self.data = deepCopy(DEFAULT_PLAYER_DATA)
    self:loadData()
    return self
end

function DataManager:loadData()
    local success, data = pcall(function()
        return playerDataStore:GetAsync(DATASTORE_KEY .. self.player.UserId)
    end)
    
    if success and data then
        -- Merge with default data to handle any new fields
        for k, v in pairs(DEFAULT_PLAYER_DATA) do
            if data[k] == nil then
                data[k] = v
            elseif type(v) == "table" and type(data[k]) == "table" then
                -- Handle nested tables
                for k2, v2 in pairs(v) do
                    if data[k][k2] == nil then
                        data[k][k2] = v2
                    end
                end
            end
        end
        self.data = data
    end
    
    return self.data
end

function DataManager:saveData()
    local success, err = pcall(function()
        playerDataStore:SetAsync(DATASTORE_KEY .. self.player.UserId, self.data)
    end)
    
    if not success then
        warn("Failed to save data for", self.player.Name, ":", err)
        return false
    end
    return true
end

function DataManager:getData()
    return self.data
end

function DataManager:updateData(callback)
    if type(callback) == "function" then
        local success, err = pcall(function()
            callback(self.data)
        end)
        
        if not success then
            warn("Error updating data:", err)
            return false
        end
        
        return self:saveData()
    end
    return false
end

-- Player data helpers
function DataManager:addCash(amount)
    return self:updateData(function(data)
        data.cash = (data.cash or 0) + amount
        data.stats.totalEarnings = (data.stats.totalEarnings or 0) + amount
    end)
end

function DataManager:addGems(amount)
    return self:updateData(function(data)
        data.gems = (data.gems or 0) + amount
    end)
end

function DataManager:addXP(amount)
    return self:updateData(function(data)
        data.xp = (data.xp or 0) + amount
        -- Add level up logic here
    end)
end

-- Clean up when player leaves
function DataManager:destroy()
    self:saveData()
end

return DataManager
