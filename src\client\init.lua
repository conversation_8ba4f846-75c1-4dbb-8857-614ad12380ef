-- init.lua (Client)
-- Main client initialization script for Grown A Streamer

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Wait for shared modules to load
repeat wait() until ReplicatedStorage:FindFirstChild("Shared")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

-- Import client systems
local PlacementSystem = require(script.Systems.PlacementSystem)

-- Import client controllers
local StreamController = require(script.Controllers.StreamController)
local PlotController = require(script.Controllers.PlotController)

-- Import client UI
local MainUI = require(script.UI.MainUI)

-- Main client module
local Client = {}
local player = Players.LocalPlayer

function Client.initialize()
    print("=== GROWN A STREAMER CLIENT STARTING ===")
    print("Player:", player.Name)
    print("UserId:", player.UserId)
    
    -- Wait for character to spawn
    if not player.Character then
        player.CharacterAdded:Wait()
    end
    
    -- Initialize shared systems
    print("\n--- Initializing Shared Systems ---")
    EventManager.initialize()
    
    -- Initialize client systems
    print("\n--- Initializing Client Systems ---")
    PlacementSystem.initialize()
    
    -- Initialize controllers
    print("\n--- Initializing Controllers ---")
    StreamController.initialize()
    PlotController.initialize()
    
    -- Initialize UI
    print("\n--- Initializing User Interface ---")
    MainUI.initialize()
    
    -- Setup welcome message
    Client.showWelcomeMessage()
    
    -- Setup cleanup on leaving
    game:BindToClose(function()
        Client.cleanup()
    end)
    
    print("\n=== GROWN A STREAMER CLIENT READY ===")
    print("Welcome to Grown A Streamer!")
    print("Press M to open the main menu")
end

function Client.showWelcomeMessage()
    -- Create welcome notification
    local screenGui = Instance.new("ScreenGui")
    screenGui.Name = "WelcomeMessage"
    screenGui.Parent = player.PlayerGui
    
    local welcomeFrame = Instance.new("Frame")
    welcomeFrame.Name = "WelcomeFrame"
    welcomeFrame.Size = UDim2.new(0, 400, 0, 200)
    welcomeFrame.Position = UDim2.new(0.5, -200, 0.5, -100)
    welcomeFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    welcomeFrame.BackgroundTransparency = 0.1
    welcomeFrame.Parent = screenGui
    
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 15)
    corner.Parent = welcomeFrame
    
    -- Title
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Size = UDim2.new(1, 0, 0, 50)
    title.BackgroundTransparency = 1
    title.Text = "🎮 Welcome to Grown A Streamer!"
    title.TextColor3 = Color3.white
    title.TextScaled = true
    title.Font = Enum.Font.GothamBold
    title.Parent = welcomeFrame
    
    -- Message
    local message = Instance.new("TextLabel")
    message.Name = "Message"
    message.Size = UDim2.new(1, -20, 0, 100)
    message.Position = UDim2.new(0, 10, 0, 50)
    message.BackgroundTransparency = 1
    message.Text = "Start your streaming journey!\n\n• Press M to open the main menu\n• Visit your plot to set up your studio\n• Buy equipment to improve your streams\n• Grow your audience and become famous!"
    message.TextColor3 = Color3.fromRGB(200, 200, 200)
    message.TextScaled = true
    message.Font = Enum.Font.Gotham
    message.TextWrapped = true
    message.Parent = welcomeFrame
    
    -- Close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 100, 0, 30)
    closeButton.Position = UDim2.new(0.5, -50, 1, -40)
    closeButton.BackgroundColor3 = Color3.fromRGB(85, 170, 85)
    closeButton.Text = "Got it!"
    closeButton.TextColor3 = Color3.white
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.GothamBold
    closeButton.Parent = welcomeFrame
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 6)
    closeCorner.Parent = closeButton
    
    closeButton.MouseButton1Click:Connect(function()
        screenGui:Destroy()
    end)
    
    -- Auto-close after 10 seconds
    game:GetService("Debris"):AddItem(screenGui, 10)
end

function Client.cleanup()
    print("\n=== GROWN A STREAMER CLIENT SHUTTING DOWN ===")
    
    -- Cleanup all systems
    MainUI.cleanup()
    PlotController.cleanup()
    StreamController.cleanup()
    PlacementSystem.cleanup()
    
    print("Client cleanup completed!")
end

-- Initialize the client
Client.initialize()

return Client
