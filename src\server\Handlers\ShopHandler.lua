-- ShopHandler.lua
-- Server-side shop and purchase handling

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)
local DataManager = require(ReplicatedStorage.Shared.Modules.DataManager)

local ShopHandler = {}

-- Player data managers
local playerDataManagers = {} -- [userId] = DataManager instance

function ShopHandler.initialize()
    print("ShopHandler: Initializing...")
    
    -- Connect shop events
    EventManager.Shop.PurchaseItem.OnServerEvent:Connect(ShopHandler.onPurchaseItem)
    EventManager.Shop.PurchaseStudio.OnServerEvent:Connect(ShopHandler.onPurchaseStudio)
    EventManager.Shop.RefreshShop.OnServerEvent:Connect(ShopHandler.onRefreshShop)
    
    -- Connect shop functions
    EventManager.Shop.GetShopItems.OnServerInvoke = ShopHandler.getShopItems
    EventManager.Shop.GetPlayerCurrency.OnServerInvoke = ShopHandler.getPlayerCurrency
    EventManager.Shop.CanAffordItem.OnServerInvoke = ShopHandler.canAffordItem
    
    -- Connect player events
    Players.PlayerAdded:Connect(ShopHandler.onPlayerAdded)
    Players.PlayerRemoving:Connect(ShopHandler.onPlayerRemoving)
    
    print("ShopHandler: Initialized successfully")
end

function ShopHandler.onPlayerAdded(player)
    -- Create data manager for player
    playerDataManagers[player.UserId] = DataManager.new(player)
end

function ShopHandler.onPlayerRemoving(player)
    -- Clean up data manager
    local dataManager = playerDataManagers[player.UserId]
    if dataManager then
        dataManager:destroy()
        playerDataManagers[player.UserId] = nil
    end
end

function ShopHandler.onPurchaseItem(player, data)
    -- Validate purchase data
    if not ShopHandler.validatePurchaseData(data) then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Invalid purchase data"
        })
        return
    end
    
    -- Check rate limit
    if not EventManager.checkRateLimit("PurchaseItem", player) then
        local remaining = EventManager.getRemainingCooldown("PurchaseItem", player)
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Rate limited",
            cooldown = remaining
        })
        return
    end
    
    -- Get player data manager
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Player data not found"
        })
        return
    end
    
    -- Validate item exists and get price
    local itemConfig = GameConfig.getEquipmentLevel(data.itemType, data.level)
    if not itemConfig then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Item not found"
        })
        return
    end
    
    -- Check if player can afford item
    local playerData = dataManager:getData()
    if not playerData or playerData.cash < itemConfig.price then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Insufficient funds",
            required = itemConfig.price,
            current = playerData.cash or 0
        })
        return
    end
    
    -- Check if player already owns this item level
    if ShopHandler.playerOwnsItem(playerData, data.itemType, data.level) then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Already owned"
        })
        return
    end
    
    -- Process purchase
    local success = dataManager:updateData(function(data)
        -- Deduct cost
        data.cash = data.cash - itemConfig.price
        
        -- Add item to inventory
        if not data.equipment then
            data.equipment = {}
        end
        if not data.equipment[data.itemType] then
            data.equipment[data.itemType] = {}
        end
        
        -- Update equipment level
        data.equipment[data.itemType].level = math.max(
            data.equipment[data.itemType].level or 0,
            data.level
        )
        data.equipment[data.itemType].owned = true
        
        -- Update stats
        data.stats.totalSpent = (data.stats.totalSpent or 0) + itemConfig.price
    end)
    
    if success then
        -- Notify client of successful purchase
        EventManager.Shop.PurchaseSuccess:FireClient(player, {
            itemType = data.itemType,
            level = data.level,
            price = itemConfig.price,
            newBalance = playerData.cash - itemConfig.price
        })
        
        -- Update currency display
        EventManager.Shop.CurrencyUpdate:FireClient(player, {
            cash = playerData.cash - itemConfig.price,
            gems = playerData.gems or 0,
            followers = playerData.stats.totalFollowers or 0,
            clout = playerData.clout or 0
        })
        
        -- Log purchase
        EventManager.logEvent("PurchaseItem", player, {
            itemType = data.itemType,
            level = data.level,
            price = itemConfig.price
        })
        
        print(player.Name, "purchased", data.itemType, "level", data.level, "for $" .. itemConfig.price)
    else
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Purchase failed - please try again"
        })
    end
end

function ShopHandler.onPurchaseStudio(player, data)
    -- Validate studio purchase data
    if not data or not data.studioType then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Invalid studio data"
        })
        return
    end
    
    -- Check rate limit
    if not EventManager.checkRateLimit("PurchaseStudio", player) then
        return
    end
    
    -- Get studio configuration
    local studioConfig = GameConfig.StudioTypes[data.studioType]
    if not studioConfig then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Studio not found"
        })
        return
    end
    
    -- Get player data manager
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Player data not found"
        })
        return
    end
    
    local playerData = dataManager:getData()
    
    -- Check if player meets requirements
    if not GameConfig.isStudioUnlocked(playerData.level, playerData.stats.totalFollowers or 0, data.studioType) then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Studio requirements not met",
            levelRequired = studioConfig.unlockLevel,
            followersRequired = studioConfig.followersRequired
        })
        return
    end
    
    -- Check if player can afford studio
    if playerData.cash < studioConfig.price then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Insufficient funds",
            required = studioConfig.price,
            current = playerData.cash
        })
        return
    end
    
    -- Check if player already owns studio
    if playerData.ownedStudios and table.find(playerData.ownedStudios, data.studioType) then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Studio already owned"
        })
        return
    end
    
    -- Process studio purchase
    local success = dataManager:updateData(function(data)
        -- Deduct cost
        data.cash = data.cash - studioConfig.price
        
        -- Add studio to owned list
        if not data.ownedStudios then
            data.ownedStudios = {"DIRT_STUDIO"} -- Default studio
        end
        table.insert(data.ownedStudios, data.studioType)
        
        -- Update stats
        data.stats.totalSpent = (data.stats.totalSpent or 0) + studioConfig.price
        data.stats.studiosOwned = #data.ownedStudios
    end)
    
    if success then
        EventManager.Shop.PurchaseSuccess:FireClient(player, {
            studioType = data.studioType,
            price = studioConfig.price,
            newBalance = playerData.cash - studioConfig.price
        })
        
        -- Update currency display
        EventManager.Shop.CurrencyUpdate:FireClient(player, {
            cash = playerData.cash - studioConfig.price,
            gems = playerData.gems or 0,
            followers = playerData.stats.totalFollowers or 0,
            clout = playerData.clout or 0
        })
        
        print(player.Name, "purchased studio", data.studioType, "for $" .. studioConfig.price)
    else
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Studio purchase failed"
        })
    end
end

function ShopHandler.onRefreshShop(player)
    -- Send updated shop data to client
    local shopItems = ShopHandler.getShopItems(player)
    local playerCurrency = ShopHandler.getPlayerCurrency(player)
    
    EventManager.Shop.RefreshShop:FireClient(player, {
        items = shopItems,
        currency = playerCurrency
    })
end

function ShopHandler.getShopItems(player)
    local items = {}
    
    -- Get equipment items
    for equipType, equipConfig in pairs(GameConfig.EquipmentTypes) do
        for level, levelData in ipairs(equipConfig.levels) do
            table.insert(items, {
                type = "equipment",
                itemType = equipType,
                level = level,
                name = levelData.name,
                description = levelData.description,
                price = levelData.price,
                multiplier = levelData.multiplier,
                category = equipConfig.category
            })
        end
    end
    
    -- Get studio items
    for studioType, studioConfig in pairs(GameConfig.StudioTypes) do
        if studioType ~= "DIRT_STUDIO" then -- Don't sell the free starter studio
            table.insert(items, {
                type = "studio",
                studioType = studioType,
                name = studioConfig.name,
                description = studioConfig.description,
                price = studioConfig.price,
                unlockLevel = studioConfig.unlockLevel,
                followersRequired = studioConfig.followersRequired,
                theme = studioConfig.theme
            })
        end
    end
    
    return items
end

function ShopHandler.getPlayerCurrency(player)
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then
        return {cash = 0, gems = 0, followers = 0, clout = 0}
    end
    
    local playerData = dataManager:getData()
    return {
        cash = playerData.cash or 0,
        gems = playerData.gems or 0,
        followers = playerData.stats.totalFollowers or 0,
        clout = playerData.clout or 0
    }
end

function ShopHandler.canAffordItem(player, itemData)
    if not itemData or not itemData.price then return false end
    
    local dataManager = playerDataManagers[player.UserId]
    if not dataManager then return false end
    
    local playerData = dataManager:getData()
    return (playerData.cash or 0) >= itemData.price
end

-- Helper functions
function ShopHandler.validatePurchaseData(data)
    return data and 
           data.itemType and 
           data.level and 
           type(data.level) == "number" and 
           data.level > 0
end

function ShopHandler.playerOwnsItem(playerData, itemType, level)
    if not playerData.equipment or not playerData.equipment[itemType] then
        return false
    end
    
    local currentLevel = playerData.equipment[itemType].level or 0
    return currentLevel >= level
end

function ShopHandler.cleanup()
    -- Clean up all data managers
    for userId, dataManager in pairs(playerDataManagers) do
        dataManager:destroy()
    end
    playerDataManagers = {}
    
    print("ShopHandler: Cleaned up")
end

return ShopHandler
