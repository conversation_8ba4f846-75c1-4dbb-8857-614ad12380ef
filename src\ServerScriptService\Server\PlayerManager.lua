local Players = game:GetService("Players")
local DataManager = require(game.ReplicatedStorage.Shared.Modules.DataManager)

local PlayerManager = {}
PlayerManager.__index = PlayerManager

function PlayerManager.new()
    local self = setmetatable({}, PlayerManager)
    self.players = {}
    self:init()
    return self
end

function PlayerManager:init()
    -- Set up player added/removed connections
    Players.PlayerAdded:Connect(function(player)
        self:playerAdded(player)
    end)
    
    Players.PlayerRemoving:Connect(function(player)
        self:playerRemoving(player)
    end)
    
    -- Handle existing players in case of hot reloading
    for _, player in ipairs(Players:GetPlayers()) do
        task.spawn(function()
            self:playerAdded(player)
        end)
    end
end

function PlayerManager:playerAdded(player)
    -- Create leaderstats
    local leaderstats = Instance.new("Folder")
    leaderstats.Name = "leaderstats"
    leaderstats.Parent = player
    
    -- Initialize data manager for this player
    local dataManager = DataManager.new(player)
    local playerData = dataManager:getData()
    
    -- Create leaderstat values
    local cash = Instance.new("IntValue")
    cash.Name = "Cash"
    cash.Value = playerData.cash or 0
    cash.Parent = leaderstats
    
    local gems = Instance.new("IntValue")
    gems.Name = "Gems"
    gems.Value = playerData.gems or 0
    gems.Parent = leaderstats
    
    local level = Instance.new("IntValue")
    level.Name = "Level"
    level.Value = playerData.level or 1
    level.Parent = leaderstats
    
    -- Store the data manager for this player
    self.players[player.UserId] = {
        dataManager = dataManager,
        leaderstats = {
            Cash = cash,
            Gems = gems,
            Level = level
        },
        currentStream = nil
    }
    
    -- Set up value changed connections
    cash.Changed:Connect(function(value)
        dataManager:updateData(function(data)
            data.cash = value
        end)
    end)
    
    gems.Changed:Connect(function(value)
        dataManager:updateData(function(data)
            data.gems = value
        end)
    end)
    
    level.Changed:Connect(function(value)
        dataManager:updateData(function(data)
            data.level = value
        end)
    end)
    
    -- Initialize player's plot and studio
    self:initializePlayerStudio(player)
end

function PlayerManager:playerRemoving(player)
    local playerData = self.players[player.UserId]
    if playerData then
        -- Save data and clean up
        playerData.dataManager:destroy()
        self.players[player.UserId] = nil
    end
end

function PlayerManager:initializePlayerStudio(player)
    -- This will be implemented to set up the player's studio plot
    -- For now, we'll just print a message
    print("Initializing studio for player:", player.Name)
end

function PlayerManager:getPlayerData(player)
    return self.players[player.UserId]
end

-- Clean up when the game is shutting down
function PlayerManager:shutdown()
    for _, player in ipairs(Players:GetPlayers()) do
        self:playerRemoving(player)
    end
end

return PlayerManager
