local DataStoreService = game:GetService("DataStoreService")
local RunService = game:GetService("RunService")
local GameConfig = require(script.Parent.GameConfig)

local DataManager = {}
DataManager.__index = DataManager

-- Auto-save tracking
local autoSaveConnections = {} -- [userId] = connection

-- Constants
local DATASTORE_KEY = "GrownStreamerData_v2_"
local AUTO_SAVE_INTERVAL = 60 -- seconds
local DEFAULT_PLAYER_DATA = {
    -- Basic player info
    cash = 100, -- Starting cash
    gems = 5, -- Starting gems
    clout = 0, -- Prestige currency
    level = 1,
    xp = 0,

    -- Studio and equipment
    currentStudio = "DIRT_STUDIO",
    ownedStudios = {"DIRT_STUDIO"},
    equipment = {
        PC = {level = 1, owned = true},
        MICROPHONE = {level = 1, owned = true},
        CAMERA = {level = 1, owned = true},
        CHAIR = {level = 1, owned = true},
        LIGHTING = {level = 0, owned = false},
        DESK = {level = 0, owned = false}
    },

    -- Placement data
    placedItems = {}, -- {itemId, itemType, level, position, rotation, zoneId}
    studioLayouts = {}, -- Saved layouts for different studios

    -- Statistics
    stats = {
        totalViews = 0,
        totalLikes = 0,
        totalFollowers = 0,
        totalEarnings = 0,
        totalSpent = 0,
        streamsCompleted = 0,
        timeStreamed = 0,
        studiosOwned = 1,
        itemsPurchased = 0,
        achievementsUnlocked = 0,
        socialInteractions = 0,
        plotsVisited = 0
    },

    -- Achievements and progression
    achievements = {}, -- {achievementId, unlockedTime, claimed}
    milestones = {}, -- Progress towards various milestones

    -- Social data
    friends = {}, -- {userId, friendSince, lastInteraction}
    blockedUsers = {}, -- {userId, blockedTime}
    studioRatings = {}, -- {studioOwnerId, rating, ratedTime}
    receivedRatings = {}, -- {fromUserId, rating, ratedTime}

    -- Daily/Event data
    dailyRewards = {
        lastClaimed = 0,
        streak = 0,
        totalClaimed = 0
    },
    eventData = {}, -- Event-specific progress and rewards

    -- Settings
    settings = {
        musicVolume = 0.5,
        sfxVolume = 0.7,
        uiScale = 1.0,
        autoSave = true,
        notifications = true,
        chatFilter = true,
        privateMode = false
    },

    -- Metadata
    metadata = {
        firstJoin = 0,
        lastSave = 0,
        totalPlayTime = 0,
        version = 2,
        migrated = false
    }
}

-- Initialize DataStore
local playerDataStore = DataStoreService:GetDataStore("PlayerData")

-- Private functions
local function deepCopy(original)
    local copy = {}
    for k, v in pairs(original) do
        if type(v) == "table" then
            v = deepCopy(v)
        end
        copy[k] = v
    end
    return copy
end

-- Public methods
function DataManager.new(player)
    local self = setmetatable({}, DataManager)
    self.player = player
    self.data = deepCopy(DEFAULT_PLAYER_DATA)
    self.lastSave = 0
    self.isDirty = false -- Track if data needs saving

    -- Load existing data
    self:loadData()

    -- Set up auto-save
    self:setupAutoSave()

    -- Initialize metadata
    if self.data.metadata.firstJoin == 0 then
        self.data.metadata.firstJoin = tick()
    end
    self.data.metadata.lastSave = tick()

    return self
end

function DataManager:loadData()
    local success, data = pcall(function()
        return playerDataStore:GetAsync(DATASTORE_KEY .. self.player.UserId)
    end)
    
    if success and data then
        -- Merge with default data to handle any new fields
        for k, v in pairs(DEFAULT_PLAYER_DATA) do
            if data[k] == nil then
                data[k] = v
            elseif type(v) == "table" and type(data[k]) == "table" then
                -- Handle nested tables
                for k2, v2 in pairs(v) do
                    if data[k][k2] == nil then
                        data[k][k2] = v2
                    end
                end
            end
        end
        self.data = data
    end
    
    return self.data
end

function DataManager:saveData()
    -- Update metadata
    self.data.metadata.lastSave = tick()

    local success, err = pcall(function()
        playerDataStore:SetAsync(DATASTORE_KEY .. self.player.UserId, self.data)
    end)

    if success then
        self.lastSave = tick()
        self.isDirty = false
        return true
    else
        warn("Failed to save data for", self.player.Name, ":", err)
        return false
    end
end

function DataManager:getData()
    return self.data
end

function DataManager:updateData(callback)
    if type(callback) == "function" then
        local success, err = pcall(function()
            callback(self.data)
        end)

        if not success then
            warn("Error updating data:", err)
            return false
        end

        self.isDirty = true
        return self:saveData()
    end
    return false
end

function DataManager:setupAutoSave()
    local userId = self.player.UserId

    -- Clean up existing connection
    if autoSaveConnections[userId] then
        autoSaveConnections[userId]:Disconnect()
    end

    -- Set up new auto-save connection
    autoSaveConnections[userId] = RunService.Heartbeat:Connect(function()
        local currentTime = tick()

        -- Auto-save every AUTO_SAVE_INTERVAL seconds if data is dirty
        if self.isDirty and (currentTime - self.lastSave) >= AUTO_SAVE_INTERVAL then
            self:saveData()
        end

        -- Update play time
        if self.data.metadata.lastSave > 0 then
            local deltaTime = currentTime - self.data.metadata.lastSave
            self.data.metadata.totalPlayTime = self.data.metadata.totalPlayTime + deltaTime
            self.data.metadata.lastSave = currentTime
        end
    end)
end

-- Player data helpers
function DataManager:addCash(amount)
    return self:updateData(function(data)
        data.cash = (data.cash or 0) + amount
        data.stats.totalEarnings = (data.stats.totalEarnings or 0) + amount
    end)
end

function DataManager:addGems(amount)
    return self:updateData(function(data)
        data.gems = (data.gems or 0) + amount
    end)
end

function DataManager:addXP(amount)
    return self:updateData(function(data)
        local oldLevel = data.level
        data.xp = (data.xp or 0) + amount

        -- Check for level up
        local requiredXP = GameConfig.getXPRequiredForLevel(data.level + 1)
        while data.xp >= requiredXP and requiredXP > 0 do
            data.level = data.level + 1
            data.xp = data.xp - requiredXP
            requiredXP = GameConfig.getXPRequiredForLevel(data.level + 1)
        end

        -- If leveled up, trigger level up event
        if data.level > oldLevel then
            self:onLevelUp(oldLevel, data.level)
        end
    end)
end

-- Equipment management
function DataManager:addEquipment(equipType, level)
    return self:updateData(function(data)
        if not data.equipment then data.equipment = {} end
        if not data.equipment[equipType] then
            data.equipment[equipType] = {level = 0, owned = false}
        end

        data.equipment[equipType].level = math.max(data.equipment[equipType].level, level)
        data.equipment[equipType].owned = true
        data.stats.itemsPurchased = (data.stats.itemsPurchased or 0) + 1
    end)
end

function DataManager:hasEquipment(equipType, level)
    local equipment = self.data.equipment and self.data.equipment[equipType]
    return equipment and equipment.owned and (equipment.level or 0) >= (level or 1)
end

-- Studio management
function DataManager:addStudio(studioType)
    return self:updateData(function(data)
        if not data.ownedStudios then data.ownedStudios = {"DIRT_STUDIO"} end

        if not table.find(data.ownedStudios, studioType) then
            table.insert(data.ownedStudios, studioType)
            data.stats.studiosOwned = #data.ownedStudios
        end
    end)
end

function DataManager:setCurrentStudio(studioType)
    if self:hasStudio(studioType) then
        return self:updateData(function(data)
            data.currentStudio = studioType
        end)
    end
    return false
end

function DataManager:hasStudio(studioType)
    return self.data.ownedStudios and table.find(self.data.ownedStudios, studioType) ~= nil
end

-- Achievement system
function DataManager:unlockAchievement(achievementId)
    if self:hasAchievement(achievementId) then return false end

    return self:updateData(function(data)
        if not data.achievements then data.achievements = {} end

        table.insert(data.achievements, {
            id = achievementId,
            unlockedTime = tick(),
            claimed = false
        })

        data.stats.achievementsUnlocked = (data.stats.achievementsUnlocked or 0) + 1
    end)
end

function DataManager:hasAchievement(achievementId)
    if not self.data.achievements then return false end

    for _, achievement in ipairs(self.data.achievements) do
        if achievement.id == achievementId then
            return true
        end
    end
    return false
end

-- Daily rewards
function DataManager:canClaimDailyReward()
    local lastClaimed = self.data.dailyRewards.lastClaimed or 0
    local currentTime = tick()
    local dayInSeconds = 24 * 60 * 60

    return (currentTime - lastClaimed) >= dayInSeconds
end

function DataManager:claimDailyReward()
    if not self:canClaimDailyReward() then return false end

    return self:updateData(function(data)
        local currentTime = tick()
        local lastClaimed = data.dailyRewards.lastClaimed or 0
        local dayInSeconds = 24 * 60 * 60

        -- Check if streak continues (claimed within 48 hours)
        if (currentTime - lastClaimed) <= (dayInSeconds * 2) then
            data.dailyRewards.streak = (data.dailyRewards.streak or 0) + 1
        else
            data.dailyRewards.streak = 1 -- Reset streak
        end

        data.dailyRewards.lastClaimed = currentTime
        data.dailyRewards.totalClaimed = (data.dailyRewards.totalClaimed or 0) + 1

        -- Calculate rewards based on streak
        local rewards = GameConfig.Economy.DailyLoginBonus
        local streakMultiplier = math.min(data.dailyRewards.streak, 7) -- Max 7x multiplier

        data.cash = data.cash + (rewards.cash * streakMultiplier)
        data.gems = data.gems + (rewards.gems * streakMultiplier)
        data.clout = (data.clout or 0) + (rewards.clout * streakMultiplier)
    end)
end

-- Social features
function DataManager:addFriend(friendUserId)
    return self:updateData(function(data)
        if not data.friends then data.friends = {} end

        -- Check if already friends
        for _, friend in ipairs(data.friends) do
            if friend.userId == friendUserId then return end
        end

        table.insert(data.friends, {
            userId = friendUserId,
            friendSince = tick(),
            lastInteraction = tick()
        })

        data.stats.socialInteractions = (data.stats.socialInteractions or 0) + 1
    end)
end

function DataManager:removeFriend(friendUserId)
    return self:updateData(function(data)
        if not data.friends then return end

        for i, friend in ipairs(data.friends) do
            if friend.userId == friendUserId then
                table.remove(data.friends, i)
                break
            end
        end
    end)
end

-- Event handlers
function DataManager:onLevelUp(oldLevel, newLevel)
    -- This can be overridden or connected to events
    print(self.player.Name, "leveled up from", oldLevel, "to", newLevel)
end

-- Clean up when player leaves
function DataManager:destroy()
    -- Disconnect auto-save
    local userId = self.player.UserId
    if autoSaveConnections[userId] then
        autoSaveConnections[userId]:Disconnect()
        autoSaveConnections[userId] = nil
    end

    -- Final save
    self:saveData()
end

return DataManager
