-- MarketplaceService.lua
-- Handles gamepass purchases and Robux transactions

local MarketplaceService = game:GetService("MarketplaceService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

-- Import other systems
local PlayerManager = require(script.Parent.Parent.Core.PlayerManager)

local MarketplaceHandler = {}

-- Gamepass tracking
local gamepassOwnership = {} -- [userId] = {gamepassId = true/false}
local pendingPurchases = {} -- [userId] = {gamepassId, timestamp}

function MarketplaceHandler.initialize()
    print("MarketplaceHandler: Initializing...")
    
    -- Connect marketplace events
    MarketplaceService.ProcessReceipt = MarketplaceHandler.processReceipt
    MarketplaceService.PromptGamePassPurchaseFinished:Connect(MarketplaceHandler.onGamepassPurchaseFinished)
    
    -- Connect shop events
    EventManager.Shop.PurchaseGamepass.OnServerEvent:Connect(MarketplaceHandler.onGamepassPurchaseRequest)
    EventManager.Shop.GetGamepassInfo.OnServerInvoke = MarketplaceHandler.getGamepassInfo
    
    -- Connect player events
    Players.PlayerAdded:Connect(MarketplaceHandler.onPlayerAdded)
    Players.PlayerRemoving:Connect(MarketplaceHandler.onPlayerRemoving)
    
    print("MarketplaceHandler: Initialized successfully")
end

function MarketplaceHandler.onPlayerAdded(player)
    -- Initialize gamepass ownership tracking
    gamepassOwnership[player.UserId] = {}
    
    -- Check ownership of all gamepasses
    MarketplaceHandler.checkAllGamepassOwnership(player)
end

function MarketplaceHandler.onPlayerRemoving(player)
    -- Clean up tracking
    gamepassOwnership[player.UserId] = nil
    pendingPurchases[player.UserId] = nil
end

function MarketplaceHandler.checkAllGamepassOwnership(player)
    -- Check ownership for each gamepass
    for gamepassType, gamepassConfig in pairs(GameConfig.Gamepasses) do
        if gamepassConfig.id and gamepassConfig.id > 0 then
            MarketplaceHandler.checkGamepassOwnership(player, gamepassConfig.id, gamepassType)
        end
    end
end

function MarketplaceHandler.checkGamepassOwnership(player, gamepassId, gamepassType)
    local success, owns = pcall(function()
        return MarketplaceService:UserOwnsGamePassAsync(player.UserId, gamepassId)
    end)
    
    if success then
        if not gamepassOwnership[player.UserId] then
            gamepassOwnership[player.UserId] = {}
        end
        
        gamepassOwnership[player.UserId][gamepassId] = owns
        
        -- If player owns the gamepass, apply benefits
        if owns then
            MarketplaceHandler.applyGamepassBenefits(player, gamepassType)
        end
    else
        warn("Failed to check gamepass ownership for", player.Name, "gamepass", gamepassId)
    end
end

function MarketplaceHandler.onGamepassPurchaseRequest(player, data)
    if not data or not data.gamepassType then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Invalid gamepass data"
        })
        return
    end
    
    local gamepassConfig = GameConfig.Gamepasses[data.gamepassType]
    if not gamepassConfig or not gamepassConfig.id or gamepassConfig.id <= 0 then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Gamepass not found"
        })
        return
    end
    
    -- Check if player already owns the gamepass
    if MarketplaceHandler.playerOwnsGamepass(player, gamepassConfig.id) then
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Already owned"
        })
        return
    end
    
    -- Track pending purchase
    pendingPurchases[player.UserId] = {
        gamepassId = gamepassConfig.id,
        gamepassType = data.gamepassType,
        timestamp = tick()
    }
    
    -- Prompt purchase
    local success, err = pcall(function()
        MarketplaceService:PromptGamePassPurchase(player, gamepassConfig.id)
    end)
    
    if not success then
        warn("Failed to prompt gamepass purchase:", err)
        pendingPurchases[player.UserId] = nil
        
        EventManager.Shop.PurchaseFailed:FireClient(player, {
            reason = "Failed to open purchase prompt"
        })
    end
end

function MarketplaceHandler.onGamepassPurchaseFinished(player, gamepassId, wasPurchased)
    local pending = pendingPurchases[player.UserId]
    
    if pending and pending.gamepassId == gamepassId then
        if wasPurchased then
            -- Update ownership tracking
            if not gamepassOwnership[player.UserId] then
                gamepassOwnership[player.UserId] = {}
            end
            gamepassOwnership[player.UserId][gamepassId] = true
            
            -- Apply gamepass benefits
            local gamepassType = pending.gamepassType
            MarketplaceHandler.applyGamepassBenefits(player, gamepassType)
            
            -- Notify client
            EventManager.Shop.PurchaseSuccess:FireClient(player, {
                gamepassType = gamepassType,
                gamepassId = gamepassId
            })
            
            print(player.Name, "purchased gamepass", gamepassType)
        else
            -- Purchase was cancelled or failed
            EventManager.Shop.PurchaseFailed:FireClient(player, {
                reason = "Purchase cancelled"
            })
        end
        
        -- Clear pending purchase
        pendingPurchases[player.UserId] = nil
    end
end

function MarketplaceHandler.processReceipt(receiptInfo)
    -- This handles developer product purchases (not gamepasses)
    -- For now, we'll just return success since we're focusing on gamepasses
    
    local player = Players:GetPlayerByUserId(receiptInfo.PlayerId)
    if not player then
        return Enum.ProductPurchaseDecision.NotProcessedYet
    end
    
    -- Log the purchase
    print("Developer product purchased:", receiptInfo.ProductId, "by", player.Name)
    
    return Enum.ProductPurchaseDecision.PurchaseGranted
end

function MarketplaceHandler.applyGamepassBenefits(player, gamepassType)
    local dataManager = PlayerManager.getDataManager(player)
    if not dataManager then return end
    
    local gamepassConfig = GameConfig.Gamepasses[gamepassType]
    if not gamepassConfig then return end
    
    -- Apply specific gamepass benefits
    if gamepassType == "AUTO_STREAM" then
        MarketplaceHandler.applyAutoStreamBenefit(player, dataManager)
    elseif gamepassType == "DOUBLE_MULTIPLIER" then
        MarketplaceHandler.applyDoubleMultiplierBenefit(player, dataManager)
    elseif gamepassType == "STARTER_PACK" then
        MarketplaceHandler.applyStarterPackBenefit(player, dataManager)
    elseif gamepassType == "ALL_HOUSES" then
        MarketplaceHandler.applyAllHousesBenefit(player, dataManager)
    elseif gamepassType == "PRO_SETUP" then
        MarketplaceHandler.applyProSetupBenefit(player, dataManager)
    elseif gamepassType == "CUSTOM_COLORS" then
        MarketplaceHandler.applyCustomColorsBenefit(player, dataManager)
    elseif gamepassType == "LAYOUT_SAVER" then
        MarketplaceHandler.applyLayoutSaverBenefit(player, dataManager)
    elseif gamepassType == "VIP_ACCESS" then
        MarketplaceHandler.applyVIPAccessBenefit(player, dataManager)
    end
    
    -- Mark gamepass as owned in player data
    dataManager:updateData(function(data)
        if not data.gamepasses then data.gamepasses = {} end
        data.gamepasses[gamepassType] = {
            owned = true,
            purchaseTime = tick()
        }
    end)
end

function MarketplaceHandler.applyAutoStreamBenefit(player, dataManager)
    -- Auto stream functionality will be handled by the streaming system
    print("Applied Auto Stream benefit to", player.Name)
end

function MarketplaceHandler.applyDoubleMultiplierBenefit(player, dataManager)
    -- Double multiplier will be applied in streaming calculations
    print("Applied Double Multiplier benefit to", player.Name)
end

function MarketplaceHandler.applyStarterPackBenefit(player, dataManager)
    dataManager:updateData(function(data)
        data.cash = data.cash + 100000 -- $100K
        data.gems = data.gems + 500
        data.clout = (data.clout or 0) + 1000
    end)
    
    print("Applied Starter Pack benefit to", player.Name)
end

function MarketplaceHandler.applyAllHousesBenefit(player, dataManager)
    dataManager:updateData(function(data)
        -- Unlock all studios
        data.ownedStudios = {}
        for studioType, _ in pairs(GameConfig.StudioTypes) do
            table.insert(data.ownedStudios, studioType)
        end
        data.stats.studiosOwned = #data.ownedStudios
    end)
    
    print("Applied All Houses benefit to", player.Name)
end

function MarketplaceHandler.applyProSetupBenefit(player, dataManager)
    dataManager:updateData(function(data)
        -- Give high-tier equipment
        for equipType, _ in pairs(GameConfig.EquipmentTypes) do
            if not data.equipment[equipType] then
                data.equipment[equipType] = {}
            end
            data.equipment[equipType].level = 5 -- Max level
            data.equipment[equipType].owned = true
        end
    end)
    
    print("Applied Pro Setup benefit to", player.Name)
end

function MarketplaceHandler.applyCustomColorsBenefit(player, dataManager)
    dataManager:updateData(function(data)
        if not data.customization then data.customization = {} end
        data.customization.colorsUnlocked = true
    end)
    
    print("Applied Custom Colors benefit to", player.Name)
end

function MarketplaceHandler.applyLayoutSaverBenefit(player, dataManager)
    dataManager:updateData(function(data)
        if not data.features then data.features = {} end
        data.features.layoutSaver = true
    end)
    
    print("Applied Layout Saver benefit to", player.Name)
end

function MarketplaceHandler.applyVIPAccessBenefit(player, dataManager)
    dataManager:updateData(function(data)
        if not data.features then data.features = {} end
        data.features.vipAccess = true
    end)
    
    print("Applied VIP Access benefit to", player.Name)
end

function MarketplaceHandler.playerOwnsGamepass(player, gamepassId)
    local ownership = gamepassOwnership[player.UserId]
    return ownership and ownership[gamepassId] == true
end

function MarketplaceHandler.getGamepassInfo(player)
    local gamepassInfo = {}
    
    for gamepassType, gamepassConfig in pairs(GameConfig.Gamepasses) do
        local owned = false
        if gamepassConfig.id and gamepassConfig.id > 0 then
            owned = MarketplaceHandler.playerOwnsGamepass(player, gamepassConfig.id)
        end
        
        gamepassInfo[gamepassType] = {
            id = gamepassConfig.id,
            name = gamepassConfig.name,
            price = gamepassConfig.price,
            description = gamepassConfig.description,
            benefits = gamepassConfig.benefits,
            owned = owned
        }
    end
    
    return gamepassInfo
end

function MarketplaceHandler.hasGamepassBenefit(player, gamepassType)
    local gamepassConfig = GameConfig.Gamepasses[gamepassType]
    if not gamepassConfig or not gamepassConfig.id or gamepassConfig.id <= 0 then
        return false
    end
    
    return MarketplaceHandler.playerOwnsGamepass(player, gamepassConfig.id)
end

function MarketplaceHandler.cleanup()
    gamepassOwnership = {}
    pendingPurchases = {}
    print("MarketplaceHandler: Cleaned up")
end

return MarketplaceHandler
