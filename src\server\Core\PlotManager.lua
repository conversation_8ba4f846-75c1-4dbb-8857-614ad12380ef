-- PlotManager.lua
-- Manages the 8-plot system with random assignment and liberation

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local Workspace = game:GetService("Workspace")

-- Import shared modules
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)
local EventManager = require(ReplicatedStorage.Shared.Events.EventManager)

local PlotManager = {}

-- Plot data structure
local plots = {} -- [plotId] = {owner = player, assignedTime = tick(), position = Vector3}
local playerPlots = {} -- [userId] = plotId
local plotQueue = {} -- Players waiting for plots

-- Plot positions (can be customized)
local PLOT_POSITIONS = {
    Vector3.new(0, 5, 0),      -- Plot 1
    Vector3.new(100, 5, 0),    -- Plot 2
    Vector3.new(200, 5, 0),    -- Plot 3
    Vector3.new(0, 5, 100),    -- Plot 4
    Vector3.new(100, 5, 100),  -- Plot 5
    Vector3.new(200, 5, 100),  -- Plot 6
    Vector3.new(0, 5, 200),    -- Plot 7
    Vector3.new(100, 5, 200),  -- Plot 8
}

local PLOT_SIZE = Vector3.new(80, 20, 80) -- Size of each plot area

function PlotManager.initialize()
    print("PlotManager: Initializing...")
    
    -- Initialize all plots as available
    for i = 1, GameConfig.Settings.TotalPlots do
        plots[i] = {
            owner = nil,
            assignedTime = 0,
            position = PLOT_POSITIONS[i] or Vector3.new(i * 100, 5, 0),
            liberated = true
        }
    end
    
    -- Connect player events
    Players.PlayerAdded:Connect(PlotManager.onPlayerAdded)
    Players.PlayerRemoving:Connect(PlotManager.onPlayerRemoving)
    
    -- Connect remote events
    EventManager.Data.TeleportToPlot.OnServerEvent:Connect(PlotManager.onTeleportRequest)
    EventManager.Data.GetPlayerPlot.OnServerInvoke = PlotManager.getPlayerPlot
    EventManager.Data.GetAvailablePlots.OnServerInvoke = PlotManager.getAvailablePlots
    
    -- Create plot structures in workspace
    PlotManager.createPlotStructures()
    
    print("PlotManager: Initialized with", GameConfig.Settings.TotalPlots, "plots")
end

function PlotManager.createPlotStructures()
    -- Create plots folder in workspace
    local plotsFolder = Workspace:FindFirstChild("Plots")
    if not plotsFolder then
        plotsFolder = Instance.new("Folder")
        plotsFolder.Name = "Plots"
        plotsFolder.Parent = Workspace
    end
    
    -- Create each plot structure
    for plotId = 1, GameConfig.Settings.TotalPlots do
        PlotManager.createPlotStructure(plotId, plotsFolder)
    end
end

function PlotManager.createPlotStructure(plotId, parent)
    local plotFolder = Instance.new("Folder")
    plotFolder.Name = "Plot" .. plotId
    plotFolder.Parent = parent
    
    local position = plots[plotId].position
    
    -- Create plot base
    local plotBase = Instance.new("Part")
    plotBase.Name = "PlotBase"
    plotBase.Size = PLOT_SIZE
    plotBase.Position = position
    plotBase.Material = Enum.Material.Grass
    plotBase.BrickColor = BrickColor.new("Bright green")
    plotBase.Anchored = true
    plotBase.Parent = plotFolder
    
    -- Create plot boundaries
    PlotManager.createPlotBoundaries(plotFolder, position, PLOT_SIZE)
    
    -- Create spawn point
    local spawnPoint = Instance.new("SpawnLocation")
    spawnPoint.Name = "PlotSpawn"
    spawnPoint.Size = Vector3.new(6, 1, 6)
    spawnPoint.Position = position + Vector3.new(0, PLOT_SIZE.Y/2 + 0.5, 0)
    spawnPoint.Material = Enum.Material.Neon
    spawnPoint.BrickColor = BrickColor.new("Bright blue")
    spawnPoint.Anchored = true
    spawnPoint.CanCollide = false
    spawnPoint.Enabled = false -- Will be enabled when player is assigned
    spawnPoint.Parent = plotFolder
    
    -- Create teleport pad
    local teleportPad = Instance.new("Part")
    teleportPad.Name = "TeleportPad"
    teleportPad.Size = Vector3.new(8, 0.5, 8)
    teleportPad.Position = position + Vector3.new(0, PLOT_SIZE.Y/2 + 0.25, -20)
    teleportPad.Material = Enum.Material.Neon
    teleportPad.BrickColor = BrickColor.new("Bright yellow")
    teleportPad.Anchored = true
    teleportPad.Parent = plotFolder
    
    -- Add teleport prompt
    local teleportPrompt = Instance.new("ProximityPrompt")
    teleportPrompt.ActionText = "Teleport to Plot"
    teleportPrompt.ObjectText = "Plot " .. plotId
    teleportPrompt.HoldDuration = 1
    teleportPrompt.MaxActivationDistance = 15
    teleportPrompt.Parent = teleportPad
    
    teleportPrompt.Triggered:Connect(function(player)
        PlotManager.teleportPlayerToPlot(player, plotId)
    end)
    
    -- Create plot info sign
    PlotManager.createPlotInfoSign(plotFolder, position, plotId)
    
    print("Created plot structure for Plot", plotId)
end

function PlotManager.createPlotBoundaries(plotFolder, position, size)
    local boundaryHeight = 10
    local boundaryThickness = 1
    
    -- Create invisible boundaries
    local boundaries = {
        {pos = Vector3.new(0, boundaryHeight/2, size.Z/2 + boundaryThickness/2), size = Vector3.new(size.X, boundaryHeight, boundaryThickness)}, -- Front
        {pos = Vector3.new(0, boundaryHeight/2, -size.Z/2 - boundaryThickness/2), size = Vector3.new(size.X, boundaryHeight, boundaryThickness)}, -- Back
        {pos = Vector3.new(size.X/2 + boundaryThickness/2, boundaryHeight/2, 0), size = Vector3.new(boundaryThickness, boundaryHeight, size.Z)}, -- Right
        {pos = Vector3.new(-size.X/2 - boundaryThickness/2, boundaryHeight/2, 0), size = Vector3.new(boundaryThickness, boundaryHeight, size.Z)}, -- Left
    }
    
    for i, boundary in ipairs(boundaries) do
        local wall = Instance.new("Part")
        wall.Name = "Boundary" .. i
        wall.Size = boundary.size
        wall.Position = position + boundary.pos
        wall.Material = Enum.Material.ForceField
        wall.BrickColor = BrickColor.new("Really red")
        wall.Transparency = 0.8
        wall.CanCollide = true
        wall.Anchored = true
        wall.Parent = plotFolder
    end
end

function PlotManager.createPlotInfoSign(plotFolder, position, plotId)
    -- Create sign post
    local signPost = Instance.new("Part")
    signPost.Name = "SignPost"
    signPost.Size = Vector3.new(0.5, 8, 0.5)
    signPost.Position = position + Vector3.new(-30, 4, -30)
    signPost.Material = Enum.Material.Wood
    signPost.BrickColor = BrickColor.new("Brown")
    signPost.Anchored = true
    signPost.Parent = plotFolder
    
    -- Create sign board
    local signBoard = Instance.new("Part")
    signBoard.Name = "SignBoard"
    signBoard.Size = Vector3.new(8, 4, 0.2)
    signBoard.Position = position + Vector3.new(-30, 6, -30)
    signBoard.Material = Enum.Material.Wood
    signBoard.BrickColor = BrickColor.new("Nougat")
    signBoard.Anchored = true
    signBoard.Parent = plotFolder
    
    -- Create sign text
    local signGui = Instance.new("SurfaceGui")
    signGui.Face = Enum.NormalId.Front
    signGui.Parent = signBoard
    
    local signText = Instance.new("TextLabel")
    signText.Size = UDim2.new(1, 0, 1, 0)
    signText.BackgroundTransparency = 1
    signText.Text = "Plot " .. plotId .. "\nAvailable"
    signText.TextColor3 = Color3.new(0, 0, 0)
    signText.TextScaled = true
    signText.Font = Enum.Font.GothamBold
    signText.Parent = signGui
    
    -- Store reference for updates (only store the plotId, not the Instance)
    signBoard:SetAttribute("PlotId", plotId)
end

function PlotManager.onPlayerAdded(player)
    -- Use spawn to avoid yielding the main thread
    spawn(function()
        -- Wait a moment for player to fully load
        wait(GameConfig.Settings.PlotAssignmentTimeout)

        -- Assign plot to player
        PlotManager.assignPlotToPlayer(player)
    end)
end

function PlotManager.onPlayerRemoving(player)
    local plotId = playerPlots[player.UserId]
    if plotId then
        PlotManager.liberatePlot(plotId, player)
    end
end

function PlotManager.assignPlotToPlayer(player)
    -- Find available plot
    local availablePlots = {}
    for plotId, plotData in pairs(plots) do
        if not plotData.owner then
            table.insert(availablePlots, plotId)
        end
    end
    
    if #availablePlots == 0 then
        -- No plots available, add to queue
        table.insert(plotQueue, player)
        print("No plots available for", player.Name, "- added to queue")
        return false
    end
    
    -- Randomly select from available plots
    local selectedPlotId = availablePlots[math.random(1, #availablePlots)]
    
    -- Assign plot
    plots[selectedPlotId].owner = player
    plots[selectedPlotId].assignedTime = tick()
    plots[selectedPlotId].liberated = false
    playerPlots[player.UserId] = selectedPlotId
    
    -- Update plot visuals
    PlotManager.updatePlotVisuals(selectedPlotId, player)
    
    -- Teleport player to their plot
    PlotManager.teleportPlayerToPlot(player, selectedPlotId)
    
    -- Notify player
    EventManager.Data.PlotAssigned:FireClient(player, {
        plotId = selectedPlotId,
        position = plots[selectedPlotId].position
    })
    
    print("Assigned Plot", selectedPlotId, "to", player.Name)
    return true
end

function PlotManager.liberatePlot(plotId, player)
    if not plots[plotId] or plots[plotId].owner ~= player then
        return false
    end
    
    -- Clear plot data
    plots[plotId].owner = nil
    plots[plotId].assignedTime = 0
    plots[plotId].liberated = true
    playerPlots[player.UserId] = nil
    
    -- Update plot visuals
    PlotManager.updatePlotVisuals(plotId, nil)
    
    -- Notify all clients
    EventManager.Data.PlotLiberated:FireAllClients({
        plotId = plotId,
        position = plots[plotId].position
    })
    
    -- Check queue for waiting players
    if #plotQueue > 0 then
        local nextPlayer = table.remove(plotQueue, 1)
        if nextPlayer and nextPlayer.Parent then
            wait(GameConfig.Settings.PlotLiberationDelay)
            PlotManager.assignPlotToPlayer(nextPlayer)
        end
    end
    
    print("Liberated Plot", plotId, "from", player.Name)
    return true
end

function PlotManager.updatePlotVisuals(plotId, owner)
    local plotFolder = Workspace.Plots:FindFirstChild("Plot" .. plotId)
    if not plotFolder then return end
    
    -- Update spawn point
    local spawnPoint = plotFolder:FindFirstChild("PlotSpawn")
    if spawnPoint then
        spawnPoint.Enabled = owner ~= nil
        if owner then
            spawnPoint.BrickColor = BrickColor.new("Bright green")
        else
            spawnPoint.BrickColor = BrickColor.new("Bright blue")
        end
    end
    
    -- Update sign
    local signBoard = plotFolder:FindFirstChild("SignBoard")
    if signBoard then
        local signGui = signBoard:FindFirstChild("SurfaceGui")
        if signGui then
            local signText = signGui:FindFirstChild("TextLabel")
            if signText then
                if owner then
                    signText.Text = "Plot " .. plotId .. "\n" .. owner.Name .. "'s Studio"
                    signText.TextColor3 = Color3.new(0, 0.5, 0)
                else
                    signText.Text = "Plot " .. plotId .. "\nAvailable"
                    signText.TextColor3 = Color3.new(0, 0, 0)
                end
            end
        end
    end
end

function PlotManager.teleportPlayerToPlot(player, plotId)
    if not plots[plotId] then return false end
    
    local character = player.Character
    if not character or not character:FindFirstChild("HumanoidRootPart") then
        return false
    end
    
    local plotPosition = plots[plotId].position
    local teleportPosition = plotPosition + Vector3.new(0, 10, 0)
    
    character.HumanoidRootPart.CFrame = CFrame.new(teleportPosition)
    
    print("Teleported", player.Name, "to Plot", plotId)
    return true
end

function PlotManager.onTeleportRequest(player, data)
    local plotId = data and data.plotId
    if not plotId then return end
    
    -- Check if player can teleport to this plot
    local playerPlotId = playerPlots[player.UserId]
    if plotId ~= playerPlotId then
        -- Can only teleport to own plot or visit others (implement visiting later)
        return
    end
    
    PlotManager.teleportPlayerToPlot(player, plotId)
end

function PlotManager.getPlayerPlot(player)
    local plotId = playerPlots[player.UserId]
    if not plotId then return nil end
    
    return {
        plotId = plotId,
        position = plots[plotId].position,
        assignedTime = plots[plotId].assignedTime
    }
end

function PlotManager.getAvailablePlots()
    local available = {}
    for plotId, plotData in pairs(plots) do
        if not plotData.owner then
            table.insert(available, {
                plotId = plotId,
                position = plotData.position
            })
        end
    end
    return available
end

function PlotManager.getPlotOwner(plotId)
    return plots[plotId] and plots[plotId].owner
end

function PlotManager.getAllPlots()
    local allPlots = {}
    for plotId, plotData in pairs(plots) do
        table.insert(allPlots, {
            plotId = plotId,
            owner = plotData.owner and plotData.owner.Name or nil,
            position = plotData.position,
            assignedTime = plotData.assignedTime,
            liberated = plotData.liberated
        })
    end
    return allPlots
end

function PlotManager.cleanup()
    plots = {}
    playerPlots = {}
    plotQueue = {}
    print("PlotManager: Cleaned up")
end

return PlotManager
