local ReplicatedStorage = game:GetService("ReplicatedStorage")
local GameConfig = require(ReplicatedStorage.Shared.Modules.GameConfig)

local StreamSystem = {}
StreamSystem.__index = StreamSystem

-- Remote events for client communication
local Events = Instance.new("Folder")
Events.Name = "StreamEvents"

local StartStreamEvent = Instance.new("RemoteEvent")
StartStreamEvent.Name = "StartStream"
StartStreamEvent.Parent = Events

local StopStreamEvent = Instance.new("RemoteEvent")
StopStreamEvent.Name = "StopStream"
StopStreamEvent.Parent = Events

local UpdateStreamStatsEvent = Instance.new("RemoteEvent")
UpdateStreamStatsEvent.Name = "UpdateStreamStats"
UpdateStreamStatsEvent.Parent = Events

Events.Parent = ReplicatedStorage

-- Stream class
local Stream = {}
Stream.__index = Stream

function Stream.new(player, playerData)
    local self = setmetatable({}, Stream)
    
    self.player = player
    self.playerData = playerData
    self.isStreaming = false
    self.startTime = 0
    self.viewers = 0
    self.likes = 0
    self.followers = 0
    self.earnings = 0
    
    -- Set up stream stats
    self.stats = {
        peakViewers = 0,
        totalViewTime = 0,
        newFollowers = 0,
        totalLikes = 0,
        totalEarnings = 0
    }
    
    return self
end

function Stream:start()
    if self.isStreaming then return end
    
    self.isStreaming = true
    self.startTime = os.time()
    self.viewers = 0
    self.likes = 0
    self.followers = 0
    self.earnings = 0
    
    -- Reset stats for this stream
    self.stats = {
        peakViewers = 0,
        totalViewTime = 0,
        newFollowers = 0,
        totalLikes = 0,
        totalEarnings = 0
    }
    
    -- Notify client
    UpdateStreamStatsEvent:FireClient(self.player, {
        isStreaming = true,
        viewers = self.viewers,
        likes = self.likes,
        followers = self.followers,
        earnings = self.earnings
    })
    
    -- Start stream update loop
    self:updateStream()
end

function Stream:stop()
    if not self.isStreaming then return end
    
    self.isStreaming = false
    local streamDuration = os.time() - self.startTime
    
    -- Update player data
    self.playerData.dataManager:updateData(function(data)
        data.stats.totalViews = (data.stats.totalViews or 0) + self.stats.totalViewTime
        data.stats.totalLikes = (data.stats.totalLikes or 0) + self.stats.totalLikes
        data.stats.totalFollowers = (data.stats.totalFollowers or 0) + self.stats.newFollowers
        data.stats.totalEarnings = (data.stats.totalEarnings or 0) + self.earnings
        data.stats.streamsCompleted = (data.stats.streamsCompleted or 0) + 1
        data.stats.timeStreamed = (data.stats.timeStreamed or 0) + streamDuration
        
        -- Add earnings to player's cash
        data.cash = (data.cash or 0) + self.earnings
    end)
    
    -- Update leaderstats
    local leaderstats = self.player:FindFirstChild("leaderstats")
    if leaderstats then
        local cash = leaderstats:FindFirstChild("Cash")
        if cash then
            cash.Value = cash.Value + self.earnings
        end
    end
    
    -- Notify client
    UpdateStreamStatsEvent:FireClient(self.player, {
        isStreaming = false,
        viewers = 0,
        likes = 0,
        followers = 0,
        earnings = self.earnings
    })
    
    -- Return final stats
    return {
        duration = streamDuration,
        peakViewers = self.stats.peakViewers,
        totalViewTime = self.stats.totalViewTime,
        newFollowers = self.stats.newFollowers,
        totalLikes = self.stats.totalLikes,
        earnings = self.earnings
    }
end

function Stream:calculateViewerCount()
    local playerData = self.playerData.dataManager:getData()
    local studioConfig = GameConfig.StudioTypes[playerData.currentStudio] or GameConfig.StudioTypes["DIRT_STUDIO"]
    
    -- Base viewers from studio type
    local viewers = studioConfig.baseViewers or 5
    
    -- Apply equipment multipliers
    for equipmentType, equipmentData in pairs(playerData.equipment or {}) do
        local config = GameConfig.EquipmentTypes[equipmentType]
        if config then
            local level = math.min(equipmentData.level or 1, #config.levels)
            local multiplier = config.levels[level].multiplier or 1
            viewers = viewers * multiplier
        end
    end
    
    -- Add some randomness
    viewers = viewers * (0.8 + math.random() * 0.4)
    
    -- Round to nearest integer
    return math.floor(viewers + 0.5)
end

function Stream:updateStream()
    if not self.isStreaming then return end
    
    local currentTime = os.time()
    local deltaTime = currentTime - self.startTime
    
    -- Check if stream has reached maximum length
    if deltaTime >= GameConfig.Settings.MaxStreamLength then
        self:stop()
        return
    end
    
    -- Update viewer count
    self.viewers = self:calculateViewerCount()
    
    -- Calculate likes and followers
    local newLikes = 0
    local newFollowers = 0
    
    for _ = 1, self.viewers do
        -- Chance for each viewer to like
        if math.random() < GameConfig.Economy.BaseLikeChance then
            newLikes = newLikes + 1
        end
        
        -- Chance for each viewer to follow
        if math.random() < GameConfig.Economy.BaseFollowChance then
            newFollowers = newFollowers + 1
        end
    end
    
    -- Calculate earnings
    local earnings = self.viewers * GameConfig.Economy.BaseCashPerViewer
    
    -- Update stats
    self.likes = self.likes + newLikes
    self.followers = self.followers + newFollowers
    self.earnings = self.earnings + earnings
    
    self.stats.peakViewers = math.max(self.stats.peakViewers, self.viewers)
    self.stats.totalViewTime = self.stats.totalViewTime + self.viewers
    self.stats.newFollowers = self.stats.newFollowers + newFollowers
    self.stats.totalLikes = self.stats.totalLikes + newLikes
    self.stats.totalEarnings = self.stats.totalEarnings + earnings
    
    -- Update client
    UpdateStreamStatsEvent:FireClient(self.player, {
        isStreaming = true,
        viewers = self.viewers,
        likes = self.likes,
        followers = self.followers,
        earnings = self.earnings
    })
    
    -- Schedule next update
    task.delay(GameConfig.Settings.StreamTickRate, function()
        self:updateStream()
    end)
end

-- StreamSystem methods
function StreamSystem.new(playerManager)
    local self = setmetatable({}, StreamSystem)
    self.playerManager = playerManager
    self.activeStreams = {}
    
    -- Set up remote event handlers
    StartStreamEvent.OnServerEvent:Connect(function(player)
        self:startStream(player)
    end)
    
    StopStreamEvent.OnServerEvent:Connect(function(player)
        self:stopStream(player)
    end)
    
    return self
end

function StreamSystem:startStream(player)
    local playerData = self.playerManager:getPlayerData(player)
    if not playerData then return end
    
    -- Stop existing stream if any
    self:stopStream(player)
    
    -- Create and start new stream
    local stream = Stream.new(player, playerData)
    self.activeStreams[player.UserId] = stream
    stream:start()
end

function StreamSystem:stopStream(player)
    local playerData = self.playerManager:getPlayerData(player)
    if not playerData then return end
    
    local stream = self.activeStreams[player.UserId]
    if stream then
        local stats = stream:stop()
        self.activeStreams[player.UserId] = nil
        return stats
    end
    return nil
end

function StreamSystem:getPlayerStream(player)
    return self.activeStreams[player.UserId]
end

-- Clean up when player leaves
function StreamSystem:playerRemoving(player)
    local stream = self.activeStreams[player.UserId]
    if stream then
        stream:stop()
        self.activeStreams[player.UserId] = nil
    end
end

return StreamSystem
